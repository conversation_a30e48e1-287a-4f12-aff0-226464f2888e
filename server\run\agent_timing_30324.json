[{"name": "Process Start", "start": 1753492633171, "end": 1753492637362, "duration": 4191, "pid": 30324, "index": 0}, {"name": "Application Start", "start": 1753492637364, "end": 1753492639115, "duration": 1751, "pid": 30324, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753492637389, "end": 1753492637430, "duration": 41, "pid": 30324, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753492637430, "end": 1753492637508, "duration": 78, "pid": 30324, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753492637432, "end": 1753492637433, "duration": 1, "pid": 30324, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753492637438, "end": 1753492637438, "duration": 0, "pid": 30324, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753492637440, "end": 1753492637440, "duration": 0, "pid": 30324, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753492637441, "end": 1753492637442, "duration": 1, "pid": 30324, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753492637443, "end": 1753492637445, "duration": 2, "pid": 30324, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753492637446, "end": 1753492637447, "duration": 1, "pid": 30324, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753492637449, "end": 1753492637450, "duration": 1, "pid": 30324, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753492637451, "end": 1753492637451, "duration": 0, "pid": 30324, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753492637453, "end": 1753492637454, "duration": 1, "pid": 30324, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753492637455, "end": 1753492637456, "duration": 1, "pid": 30324, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753492637457, "end": 1753492637458, "duration": 1, "pid": 30324, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753492637466, "end": 1753492637467, "duration": 1, "pid": 30324, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753492637469, "end": 1753492637470, "duration": 1, "pid": 30324, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753492637475, "end": 1753492637475, "duration": 0, "pid": 30324, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753492637477, "end": 1753492637477, "duration": 0, "pid": 30324, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753492637479, "end": 1753492637479, "duration": 0, "pid": 30324, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753492637481, "end": 1753492637482, "duration": 1, "pid": 30324, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753492637483, "end": 1753492637484, "duration": 1, "pid": 30324, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753492637485, "end": 1753492637486, "duration": 1, "pid": 30324, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753492637487, "end": 1753492637488, "duration": 1, "pid": 30324, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753492637490, "end": 1753492637490, "duration": 0, "pid": 30324, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753492637491, "end": 1753492637493, "duration": 2, "pid": 30324, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753492637496, "end": 1753492637497, "duration": 1, "pid": 30324, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753492637502, "end": 1753492637503, "duration": 1, "pid": 30324, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753492637506, "end": 1753492637507, "duration": 1, "pid": 30324, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753492637507, "end": 1753492637507, "duration": 0, "pid": 30324, "index": 29}, {"name": "Load extend/agent.js", "start": 1753492637509, "end": 1753492637621, "duration": 112, "pid": 30324, "index": 30}, {"name": "Require(26) node_modules/egg-security/app/extend/agent.js", "start": 1753492637511, "end": 1753492637514, "duration": 3, "pid": 30324, "index": 31}, {"name": "Require(27) node_modules/egg-schedule/app/extend/agent.js", "start": 1753492637517, "end": 1753492637603, "duration": 86, "pid": 30324, "index": 32}, {"name": "Require(28) node_modules/egg-logrotator/app/extend/agent.js", "start": 1753492637605, "end": 1753492637607, "duration": 2, "pid": 30324, "index": 33}, {"name": "Load extend/context.js", "start": 1753492637621, "end": 1753492637714, "duration": 93, "pid": 30324, "index": 34}, {"name": "Require(29) node_modules/egg-security/app/extend/context.js", "start": 1753492637622, "end": 1753492637648, "duration": 26, "pid": 30324, "index": 35}, {"name": "Require(30) node_modules/egg-jsonp/app/extend/context.js", "start": 1753492637649, "end": 1753492637654, "duration": 5, "pid": 30324, "index": 36}, {"name": "Require(31) node_modules/egg-i18n/app/extend/context.js", "start": 1753492637655, "end": 1753492637656, "duration": 1, "pid": 30324, "index": 37}, {"name": "Require(32) node_modules/egg-multipart/app/extend/context.js", "start": 1753492637658, "end": 1753492637689, "duration": 31, "pid": 30324, "index": 38}, {"name": "Require(33) node_modules/egg-view/app/extend/context.js", "start": 1753492637691, "end": 1753492637694, "duration": 3, "pid": 30324, "index": 39}, {"name": "Require(34) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753492637697, "end": 1753492637698, "duration": 1, "pid": 30324, "index": 40}, {"name": "Require(35) node_modules/egg/app/extend/context.js", "start": 1753492637699, "end": 1753492637703, "duration": 4, "pid": 30324, "index": 41}, {"name": "Require(36) app/extend/context.js", "start": 1753492637703, "end": 1753492637704, "duration": 1, "pid": 30324, "index": 42}, {"name": "Load agent.js", "start": 1753492637714, "end": 1753492637787, "duration": 73, "pid": 30324, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1753492637716, "end": 1753492637716, "duration": 0, "pid": 30324, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1753492637718, "end": 1753492637718, "duration": 0, "pid": 30324, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1753492637719, "end": 1753492637737, "duration": 18, "pid": 30324, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1753492637738, "end": 1753492637742, "duration": 4, "pid": 30324, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1753492637744, "end": 1753492637763, "duration": 19, "pid": 30324, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1753492637763, "end": 1753492637764, "duration": 1, "pid": 30324, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1753492637765, "end": 1753492637766, "duration": 1, "pid": 30324, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1753492637768, "end": 1753492637784, "duration": 16, "pid": 30324, "index": 51}, {"name": "Require(45) node_modules/egg/agent.js", "start": 1753492637785, "end": 1753492637786, "duration": 1, "pid": 30324, "index": 52}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753492637796, "end": 1753492638818, "duration": 1022, "pid": 30324, "index": 53}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1753492637797, "end": 1753492638790, "duration": 993, "pid": 30324, "index": 54}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1753492637797, "end": 1753492639098, "duration": 1301, "pid": 30324, "index": 55}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753492638546, "end": 1753492638664, "duration": 118, "pid": 30324, "index": 56}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753492638582, "end": 1753492638814, "duration": 232, "pid": 30324, "index": 57}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753492638731, "end": 1753492639111, "duration": 380, "pid": 30324, "index": 58}]