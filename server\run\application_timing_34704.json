[{"name": "Process Start", "start": 1753492639339, "end": 1753492641077, "duration": 1738, "pid": 34704, "index": 0}, {"name": "Application Start", "start": 1753492641078, "end": 1753492643179, "duration": 2101, "pid": 34704, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753492641095, "end": 1753492641130, "duration": 35, "pid": 34704, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753492641130, "end": 1753492641182, "duration": 52, "pid": 34704, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753492641132, "end": 1753492641132, "duration": 0, "pid": 34704, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753492641137, "end": 1753492641138, "duration": 1, "pid": 34704, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753492641139, "end": 1753492641139, "duration": 0, "pid": 34704, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753492641140, "end": 1753492641141, "duration": 1, "pid": 34704, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753492641142, "end": 1753492641143, "duration": 1, "pid": 34704, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753492641144, "end": 1753492641144, "duration": 0, "pid": 34704, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753492641145, "end": 1753492641146, "duration": 1, "pid": 34704, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753492641146, "end": 1753492641147, "duration": 1, "pid": 34704, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753492641148, "end": 1753492641149, "duration": 1, "pid": 34704, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753492641150, "end": 1753492641151, "duration": 1, "pid": 34704, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753492641153, "end": 1753492641154, "duration": 1, "pid": 34704, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753492641155, "end": 1753492641155, "duration": 0, "pid": 34704, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753492641156, "end": 1753492641157, "duration": 1, "pid": 34704, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753492641158, "end": 1753492641158, "duration": 0, "pid": 34704, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753492641159, "end": 1753492641160, "duration": 1, "pid": 34704, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753492641160, "end": 1753492641161, "duration": 1, "pid": 34704, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753492641162, "end": 1753492641162, "duration": 0, "pid": 34704, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753492641163, "end": 1753492641164, "duration": 1, "pid": 34704, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753492641164, "end": 1753492641165, "duration": 1, "pid": 34704, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753492641166, "end": 1753492641167, "duration": 1, "pid": 34704, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753492641169, "end": 1753492641169, "duration": 0, "pid": 34704, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753492641171, "end": 1753492641171, "duration": 0, "pid": 34704, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753492641173, "end": 1753492641174, "duration": 1, "pid": 34704, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753492641177, "end": 1753492641178, "duration": 1, "pid": 34704, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753492641181, "end": 1753492641181, "duration": 0, "pid": 34704, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753492641182, "end": 1753492641182, "duration": 0, "pid": 34704, "index": 29}, {"name": "Load extend/application.js", "start": 1753492641183, "end": 1753492641302, "duration": 119, "pid": 34704, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753492641184, "end": 1753492641185, "duration": 1, "pid": 34704, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753492641186, "end": 1753492641188, "duration": 2, "pid": 34704, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753492641189, "end": 1753492641197, "duration": 8, "pid": 34704, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753492641199, "end": 1753492641207, "duration": 8, "pid": 34704, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753492641209, "end": 1753492641211, "duration": 2, "pid": 34704, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753492641213, "end": 1753492641216, "duration": 3, "pid": 34704, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753492641218, "end": 1753492641286, "duration": 68, "pid": 34704, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753492641289, "end": 1753492641294, "duration": 5, "pid": 34704, "index": 38}, {"name": "Load extend/request.js", "start": 1753492641302, "end": 1753492641321, "duration": 19, "pid": 34704, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753492641310, "end": 1753492641312, "duration": 2, "pid": 34704, "index": 40}, {"name": "Load extend/response.js", "start": 1753492641321, "end": 1753492641343, "duration": 22, "pid": 34704, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753492641329, "end": 1753492641333, "duration": 4, "pid": 34704, "index": 42}, {"name": "Load extend/context.js", "start": 1753492641343, "end": 1753492641413, "duration": 70, "pid": 34704, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753492641344, "end": 1753492641361, "duration": 17, "pid": 34704, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753492641361, "end": 1753492641364, "duration": 3, "pid": 34704, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753492641365, "end": 1753492641365, "duration": 0, "pid": 34704, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753492641367, "end": 1753492641392, "duration": 25, "pid": 34704, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753492641394, "end": 1753492641395, "duration": 1, "pid": 34704, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753492641398, "end": 1753492641398, "duration": 0, "pid": 34704, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753492641399, "end": 1753492641403, "duration": 4, "pid": 34704, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753492641404, "end": 1753492641405, "duration": 1, "pid": 34704, "index": 51}, {"name": "Load extend/helper.js", "start": 1753492641413, "end": 1753492641468, "duration": 55, "pid": 34704, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753492641414, "end": 1753492641448, "duration": 34, "pid": 34704, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753492641455, "end": 1753492641456, "duration": 1, "pid": 34704, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753492641457, "end": 1753492641457, "duration": 0, "pid": 34704, "index": 55}, {"name": "Load app.js", "start": 1753492641468, "end": 1753492641562, "duration": 94, "pid": 34704, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753492641469, "end": 1753492641470, "duration": 1, "pid": 34704, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753492641470, "end": 1753492641474, "duration": 4, "pid": 34704, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753492641475, "end": 1753492641495, "duration": 20, "pid": 34704, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753492641496, "end": 1753492641513, "duration": 17, "pid": 34704, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753492641513, "end": 1753492641527, "duration": 14, "pid": 34704, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753492641528, "end": 1753492641529, "duration": 1, "pid": 34704, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753492641530, "end": 1753492641532, "duration": 2, "pid": 34704, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753492641533, "end": 1753492641534, "duration": 1, "pid": 34704, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753492641534, "end": 1753492641534, "duration": 0, "pid": 34704, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753492641535, "end": 1753492641536, "duration": 1, "pid": 34704, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753492641537, "end": 1753492641537, "duration": 0, "pid": 34704, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753492641538, "end": 1753492641538, "duration": 0, "pid": 34704, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753492641539, "end": 1753492641540, "duration": 1, "pid": 34704, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753492641540, "end": 1753492641543, "duration": 3, "pid": 34704, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753492641544, "end": 1753492641560, "duration": 16, "pid": 34704, "index": 71}, {"name": "Require(62) app.js", "start": 1753492641561, "end": 1753492641562, "duration": 1, "pid": 34704, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753492641575, "end": 1753492643169, "duration": 1594, "pid": 34704, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753492642217, "end": 1753492642328, "duration": 111, "pid": 34704, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753492642250, "end": 1753492643122, "duration": 872, "pid": 34704, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753492642379, "end": 1753492643178, "duration": 799, "pid": 34704, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753492642380, "end": 1753492642916, "duration": 536, "pid": 34704, "index": 77}, {"name": "Load Service", "start": 1753492642380, "end": 1753492642559, "duration": 179, "pid": 34704, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753492642380, "end": 1753492642559, "duration": 179, "pid": 34704, "index": 79}, {"name": "Load Middleware", "start": 1753492642559, "end": 1753492642741, "duration": 182, "pid": 34704, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753492642559, "end": 1753492642726, "duration": 167, "pid": 34704, "index": 81}, {"name": "Load Controller", "start": 1753492642742, "end": 1753492642863, "duration": 121, "pid": 34704, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753492642742, "end": 1753492642863, "duration": 121, "pid": 34704, "index": 83}, {"name": "Load Router", "start": 1753492642863, "end": 1753492642885, "duration": 22, "pid": 34704, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753492642864, "end": 1753492642866, "duration": 2, "pid": 34704, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753492642867, "end": 1753492642915, "duration": 48, "pid": 34704, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753492643178, "end": 1753492643178, "duration": 0, "pid": 34704, "index": 87}]