[{"name": "Process Start", "start": 1753440753752, "end": 1753440755657, "duration": 1905, "pid": 27484, "index": 0}, {"name": "Application Start", "start": 1753440755659, "end": 1753440757835, "duration": 2176, "pid": 27484, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753440755677, "end": 1753440755711, "duration": 34, "pid": 27484, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753440755711, "end": 1753440755757, "duration": 46, "pid": 27484, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753440755712, "end": 1753440755712, "duration": 0, "pid": 27484, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753440755718, "end": 1753440755718, "duration": 0, "pid": 27484, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753440755719, "end": 1753440755719, "duration": 0, "pid": 27484, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753440755720, "end": 1753440755721, "duration": 1, "pid": 27484, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753440755722, "end": 1753440755722, "duration": 0, "pid": 27484, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753440755723, "end": 1753440755724, "duration": 1, "pid": 27484, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753440755724, "end": 1753440755725, "duration": 1, "pid": 27484, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753440755725, "end": 1753440755726, "duration": 1, "pid": 27484, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753440755726, "end": 1753440755727, "duration": 1, "pid": 27484, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753440755727, "end": 1753440755728, "duration": 1, "pid": 27484, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753440755730, "end": 1753440755731, "duration": 1, "pid": 27484, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753440755732, "end": 1753440755732, "duration": 0, "pid": 27484, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753440755733, "end": 1753440755734, "duration": 1, "pid": 27484, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753440755735, "end": 1753440755735, "duration": 0, "pid": 27484, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753440755736, "end": 1753440755737, "duration": 1, "pid": 27484, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753440755738, "end": 1753440755738, "duration": 0, "pid": 27484, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753440755739, "end": 1753440755740, "duration": 1, "pid": 27484, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753440755740, "end": 1753440755741, "duration": 1, "pid": 27484, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753440755741, "end": 1753440755742, "duration": 1, "pid": 27484, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753440755742, "end": 1753440755743, "duration": 1, "pid": 27484, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753440755744, "end": 1753440755744, "duration": 0, "pid": 27484, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753440755747, "end": 1753440755748, "duration": 1, "pid": 27484, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753440755750, "end": 1753440755751, "duration": 1, "pid": 27484, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753440755753, "end": 1753440755754, "duration": 1, "pid": 27484, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753440755756, "end": 1753440755756, "duration": 0, "pid": 27484, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753440755757, "end": 1753440755757, "duration": 0, "pid": 27484, "index": 29}, {"name": "Load extend/application.js", "start": 1753440755758, "end": 1753440755862, "duration": 104, "pid": 27484, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753440755759, "end": 1753440755759, "duration": 0, "pid": 27484, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753440755760, "end": 1753440755762, "duration": 2, "pid": 27484, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753440755763, "end": 1753440755770, "duration": 7, "pid": 27484, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753440755772, "end": 1753440755779, "duration": 7, "pid": 27484, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753440755781, "end": 1753440755784, "duration": 3, "pid": 27484, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753440755785, "end": 1753440755787, "duration": 2, "pid": 27484, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753440755788, "end": 1753440755851, "duration": 63, "pid": 27484, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753440755853, "end": 1753440755857, "duration": 4, "pid": 27484, "index": 38}, {"name": "Load extend/request.js", "start": 1753440755862, "end": 1753440755877, "duration": 15, "pid": 27484, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753440755869, "end": 1753440755870, "duration": 1, "pid": 27484, "index": 40}, {"name": "Load extend/response.js", "start": 1753440755877, "end": 1753440755894, "duration": 17, "pid": 27484, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753440755884, "end": 1753440755888, "duration": 4, "pid": 27484, "index": 42}, {"name": "Load extend/context.js", "start": 1753440755894, "end": 1753440755964, "duration": 70, "pid": 27484, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753440755894, "end": 1753440755913, "duration": 19, "pid": 27484, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753440755913, "end": 1753440755916, "duration": 3, "pid": 27484, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753440755917, "end": 1753440755918, "duration": 1, "pid": 27484, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753440755919, "end": 1753440755944, "duration": 25, "pid": 27484, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753440755945, "end": 1753440755949, "duration": 4, "pid": 27484, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753440755951, "end": 1753440755952, "duration": 1, "pid": 27484, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753440755953, "end": 1753440755956, "duration": 3, "pid": 27484, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753440755957, "end": 1753440755957, "duration": 0, "pid": 27484, "index": 51}, {"name": "Load extend/helper.js", "start": 1753440755965, "end": 1753440756005, "duration": 40, "pid": 27484, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753440755966, "end": 1753440755991, "duration": 25, "pid": 27484, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753440755997, "end": 1753440755997, "duration": 0, "pid": 27484, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753440755998, "end": 1753440755999, "duration": 1, "pid": 27484, "index": 55}, {"name": "Load app.js", "start": 1753440756005, "end": 1753440756092, "duration": 87, "pid": 27484, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753440756006, "end": 1753440756006, "duration": 0, "pid": 27484, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753440756007, "end": 1753440756009, "duration": 2, "pid": 27484, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753440756010, "end": 1753440756023, "duration": 13, "pid": 27484, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753440756024, "end": 1753440756039, "duration": 15, "pid": 27484, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753440756040, "end": 1753440756055, "duration": 15, "pid": 27484, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753440756056, "end": 1753440756057, "duration": 1, "pid": 27484, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753440756058, "end": 1753440756060, "duration": 2, "pid": 27484, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753440756060, "end": 1753440756061, "duration": 1, "pid": 27484, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753440756061, "end": 1753440756065, "duration": 4, "pid": 27484, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753440756065, "end": 1753440756066, "duration": 1, "pid": 27484, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753440756068, "end": 1753440756068, "duration": 0, "pid": 27484, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753440756069, "end": 1753440756069, "duration": 0, "pid": 27484, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753440756070, "end": 1753440756070, "duration": 0, "pid": 27484, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753440756071, "end": 1753440756074, "duration": 3, "pid": 27484, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753440756074, "end": 1753440756090, "duration": 16, "pid": 27484, "index": 71}, {"name": "Require(62) app.js", "start": 1753440756091, "end": 1753440756092, "duration": 1, "pid": 27484, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753440756105, "end": 1753440757813, "duration": 1708, "pid": 27484, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753440756772, "end": 1753440756905, "duration": 133, "pid": 27484, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753440756819, "end": 1753440757757, "duration": 938, "pid": 27484, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753440756984, "end": 1753440757833, "duration": 849, "pid": 27484, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753440756985, "end": 1753440757489, "duration": 504, "pid": 27484, "index": 77}, {"name": "Load Service", "start": 1753440756986, "end": 1753440757171, "duration": 185, "pid": 27484, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753440756986, "end": 1753440757171, "duration": 185, "pid": 27484, "index": 79}, {"name": "Load Middleware", "start": 1753440757171, "end": 1753440757349, "duration": 178, "pid": 27484, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753440757171, "end": 1753440757335, "duration": 164, "pid": 27484, "index": 81}, {"name": "Load Controller", "start": 1753440757349, "end": 1753440757432, "duration": 83, "pid": 27484, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753440757349, "end": 1753440757432, "duration": 83, "pid": 27484, "index": 83}, {"name": "Load Router", "start": 1753440757432, "end": 1753440757458, "duration": 26, "pid": 27484, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753440757433, "end": 1753440757435, "duration": 2, "pid": 27484, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753440757438, "end": 1753440757488, "duration": 50, "pid": 27484, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753440757834, "end": 1753440757834, "duration": 0, "pid": 27484, "index": 87}]