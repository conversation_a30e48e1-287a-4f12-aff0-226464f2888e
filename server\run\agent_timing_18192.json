[{"name": "Process Start", "start": 1753493836378, "end": 1753493841225, "duration": 4847, "pid": 18192, "index": 0}, {"name": "Application Start", "start": 1753493841229, "end": 1753493843079, "duration": 1850, "pid": 18192, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753493841254, "end": 1753493841309, "duration": 55, "pid": 18192, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753493841309, "end": 1753493841386, "duration": 77, "pid": 18192, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753493841311, "end": 1753493841313, "duration": 2, "pid": 18192, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753493841319, "end": 1753493841320, "duration": 1, "pid": 18192, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753493841322, "end": 1753493841322, "duration": 0, "pid": 18192, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753493841324, "end": 1753493841324, "duration": 0, "pid": 18192, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753493841326, "end": 1753493841327, "duration": 1, "pid": 18192, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753493841329, "end": 1753493841329, "duration": 0, "pid": 18192, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753493841331, "end": 1753493841332, "duration": 1, "pid": 18192, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753493841333, "end": 1753493841333, "duration": 0, "pid": 18192, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753493841334, "end": 1753493841336, "duration": 2, "pid": 18192, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753493841337, "end": 1753493841338, "duration": 1, "pid": 18192, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753493841339, "end": 1753493841340, "duration": 1, "pid": 18192, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753493841342, "end": 1753493841343, "duration": 1, "pid": 18192, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753493841345, "end": 1753493841346, "duration": 1, "pid": 18192, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753493841348, "end": 1753493841348, "duration": 0, "pid": 18192, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753493841351, "end": 1753493841352, "duration": 1, "pid": 18192, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753493841354, "end": 1753493841354, "duration": 0, "pid": 18192, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753493841356, "end": 1753493841356, "duration": 0, "pid": 18192, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753493841358, "end": 1753493841358, "duration": 0, "pid": 18192, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753493841360, "end": 1753493841361, "duration": 1, "pid": 18192, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753493841362, "end": 1753493841364, "duration": 2, "pid": 18192, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753493841367, "end": 1753493841367, "duration": 0, "pid": 18192, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753493841370, "end": 1753493841370, "duration": 0, "pid": 18192, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753493841373, "end": 1753493841374, "duration": 1, "pid": 18192, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753493841379, "end": 1753493841380, "duration": 1, "pid": 18192, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753493841384, "end": 1753493841385, "duration": 1, "pid": 18192, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753493841386, "end": 1753493841386, "duration": 0, "pid": 18192, "index": 29}, {"name": "Load extend/agent.js", "start": 1753493841387, "end": 1753493841527, "duration": 140, "pid": 18192, "index": 30}, {"name": "Require(26) node_modules/egg-security/app/extend/agent.js", "start": 1753493841389, "end": 1753493841391, "duration": 2, "pid": 18192, "index": 31}, {"name": "Require(27) node_modules/egg-schedule/app/extend/agent.js", "start": 1753493841395, "end": 1753493841507, "duration": 112, "pid": 18192, "index": 32}, {"name": "Require(28) node_modules/egg-logrotator/app/extend/agent.js", "start": 1753493841510, "end": 1753493841514, "duration": 4, "pid": 18192, "index": 33}, {"name": "Load extend/context.js", "start": 1753493841527, "end": 1753493841619, "duration": 92, "pid": 18192, "index": 34}, {"name": "Require(29) node_modules/egg-security/app/extend/context.js", "start": 1753493841529, "end": 1753493841551, "duration": 22, "pid": 18192, "index": 35}, {"name": "Require(30) node_modules/egg-jsonp/app/extend/context.js", "start": 1753493841552, "end": 1753493841557, "duration": 5, "pid": 18192, "index": 36}, {"name": "Require(31) node_modules/egg-i18n/app/extend/context.js", "start": 1753493841558, "end": 1753493841559, "duration": 1, "pid": 18192, "index": 37}, {"name": "Require(32) node_modules/egg-multipart/app/extend/context.js", "start": 1753493841561, "end": 1753493841593, "duration": 32, "pid": 18192, "index": 38}, {"name": "Require(33) node_modules/egg-view/app/extend/context.js", "start": 1753493841595, "end": 1753493841597, "duration": 2, "pid": 18192, "index": 39}, {"name": "Require(34) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753493841599, "end": 1753493841600, "duration": 1, "pid": 18192, "index": 40}, {"name": "Require(35) node_modules/egg/app/extend/context.js", "start": 1753493841602, "end": 1753493841607, "duration": 5, "pid": 18192, "index": 41}, {"name": "Require(36) app/extend/context.js", "start": 1753493841608, "end": 1753493841609, "duration": 1, "pid": 18192, "index": 42}, {"name": "Load agent.js", "start": 1753493841619, "end": 1753493841699, "duration": 80, "pid": 18192, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1753493841620, "end": 1753493841621, "duration": 1, "pid": 18192, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1753493841622, "end": 1753493841622, "duration": 0, "pid": 18192, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1753493841623, "end": 1753493841641, "duration": 18, "pid": 18192, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1753493841642, "end": 1753493841648, "duration": 6, "pid": 18192, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1753493841651, "end": 1753493841673, "duration": 22, "pid": 18192, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1753493841674, "end": 1753493841675, "duration": 1, "pid": 18192, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1753493841676, "end": 1753493841677, "duration": 1, "pid": 18192, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1753493841679, "end": 1753493841696, "duration": 17, "pid": 18192, "index": 51}, {"name": "Require(45) node_modules/egg/agent.js", "start": 1753493841697, "end": 1753493841698, "duration": 1, "pid": 18192, "index": 52}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753493841705, "end": 1753493842763, "duration": 1058, "pid": 18192, "index": 53}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1753493841706, "end": 1753493842734, "duration": 1028, "pid": 18192, "index": 54}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1753493841706, "end": 1753493843073, "duration": 1367, "pid": 18192, "index": 55}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753493842511, "end": 1753493842630, "duration": 119, "pid": 18192, "index": 56}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753493842547, "end": 1753493842759, "duration": 212, "pid": 18192, "index": 57}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753493842685, "end": 1753493843076, "duration": 391, "pid": 18192, "index": 58}]