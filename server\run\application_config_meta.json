{"name": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "keys": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "middleware": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "security": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "csrf": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "type": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "cookieName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "headerName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "bodyName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "queryName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "cookieOptions": {"httpOnly": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "sameSite": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "secure": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "signed": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "ignoreJSON": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "useSession": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "sessionName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "rotateWhenInvalid": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "supportedRequests": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "refererWhiteList": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "xss": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "rateLimit": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "windowMs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "max": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "message": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "statusCode": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "headers": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "paths": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "bruteForce": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "paths": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxAttempts": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "blockDuration": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "contentSecurityPolicy": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "policy": {"default-src": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "script-src": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "style-src": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "img-src": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "font-src": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "connect-src": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "headers": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "xssProtection": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "value": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "noSniff": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "value": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "frameOptions": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "value": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "hsts": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxAge": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "includeSubdomains": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "referrerPolicy": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "value": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "domainWhiteList": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "protocolWhiteList": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "defaultMiddleware": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "xframe": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "value": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "hsts": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.local.js", "maxAge": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "includeSubdomains": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "dta": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "methodnoallow": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "noopen": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "nosniff": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "referrerPolicy": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "value": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "xssProtection": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "value": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "csp": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "policy": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "ssrf": {"ipBlackList": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "ipExceptionList": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "hostnameExceptionList": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js", "checkAddress": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}}, "cors": {"origin": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "allowMethods": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "credentials": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js"}, "sequelize": {"dialect": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "host": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "port": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "database": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "username": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "password": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "timezone": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "define": {"underscored": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "freezeTableName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "redis": {"client": {"port": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "host": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "password": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "db": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "jwt": {"secret": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "expiresIn": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-jwt\\config\\config.default.js"}, "cache": {"enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "prefix": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "defaultTTL": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "layers": {"client": {"enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxAge": {"static": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "api": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "user": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "server": {"enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "ttl": {"stock": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "user": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "market": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "search": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "static": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "database": {"enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "refreshInterval": {"stock": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "index": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "industry": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}}}, "tushare": {"token": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "baseUrl": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "dataSource": {"sources": {"tushare": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "akshare": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "sina": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "eastmoney": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "netease": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "tencent": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "yahoo_finance": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "alltick": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "juhe": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "zhitu": {"priority": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "reliability": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "performance": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "costPerRequest": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "failover": {"enabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxRetries": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "retryDelay": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "healthCheckInterval": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "recoveryThreshold": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "failureThreshold": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "timeoutThreshold": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "requestOptimizer": {"batchingEnabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "throttlingEnabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "parallelEnabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "adaptiveEnabled": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "rateLimits": {"default": {"maxRequests": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "windowMs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "minBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "tushare": {"maxRequests": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "windowMs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "minBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "akshare": {"maxRequests": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "windowMs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "minBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "sina": {"maxRequests": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "windowMs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "minBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "eastmoney": {"maxRequests": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "windowMs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "minBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}, "alltick": {"maxRequests": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "windowMs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "minBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "maxBatchWait": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}, "parallel": {"maxConcurrent": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "priorityLevels": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "timeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "retryCount": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "retryDelay": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "adaptiveTimeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js"}}}, "cluster": {"listen": {"port": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "hostname": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}}, "logger": {"dir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.default.js", "level": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "consoleLevel": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "encoding": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "env": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "disableConsoleAfterReady": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "outputJSON": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "buffer": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "appLogName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "coreLogName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "agentLogName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "errorLogName": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "coreLogger": {"consoleLevel": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.local.js"}, "allowDebugAtProd": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "enablePerformanceTimer": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "enableFastContextLogger": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "auth": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "defaultUser": {"id": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "username": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js", "role": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\config.local.js"}}, "session": {"maxAge": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-session\\config\\config.default.js", "key": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-session\\config\\config.default.js", "httpOnly": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-session\\config\\config.default.js", "encrypt": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-session\\config\\config.default.js", "logValue": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-session\\config\\config.default.js"}, "helper": {"shtml": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security\\config\\config.default.js"}, "jsonp": {"limit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-jsonp\\config\\config.default.js", "callback": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-jsonp\\config\\config.default.js", "csrf": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-jsonp\\config\\config.default.js"}, "onerror": {"errorPageUrl": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-onerror\\config\\config.default.js", "appErrorFilter": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-onerror\\config\\config.default.js", "templatePath": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-onerror\\config\\config.default.js"}, "i18n": {"defaultLocale": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-i18n\\config\\config.default.js", "dirs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-i18n\\config\\config.default.js", "queryField": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-i18n\\config\\config.default.js", "cookieField": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-i18n\\config\\config.default.js", "cookieDomain": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-i18n\\config\\config.default.js", "cookieMaxAge": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-i18n\\config\\config.default.js"}, "watcher": {"type": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-watcher\\config\\config.local.js", "eventSources": {"default": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-watcher\\config\\config.default.js", "development": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-watcher\\config\\config.default.js"}}, "customLogger": {"scheduleLogger": {"consoleLevel": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-schedule\\config\\config.default.js", "file": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-schedule\\config\\config.default.js"}}, "schedule": {"directory": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-schedule\\config\\config.default.js"}, "multipart": {"mode": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "autoFields": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "defaultCharset": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "defaultParamCharset": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "fieldNameSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "fieldSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "fields": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "fileSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "files": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "fileExtensions": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "whitelist": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "allowArrayField": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "tmpdir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "cleanSchedule": {"cron": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js", "disable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart\\config\\config.default.js"}}, "development": {"watchDirs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-development\\config\\config.default.js", "ignoreDirs": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-development\\config\\config.default.js", "fastReady": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-development\\config\\config.default.js", "reloadOnDebug": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-development\\config\\config.default.js", "overrideDefault": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-development\\config\\config.default.js", "overrideIgnore": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-development\\config\\config.default.js"}, "logrotator": {"filesRotateByHour": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator\\config\\config.default.js", "hourDelimiter": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator\\config\\config.default.js", "filesRotateBySize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator\\config\\config.default.js", "maxFileSize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator\\config\\config.default.js", "maxFiles": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator\\config\\config.default.js", "rotateDuration": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator\\config\\config.default.js", "maxDays": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator\\config\\config.default.js"}, "static": {"prefix": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-static\\config\\config.default.js", "dir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-static\\config\\config.default.js", "dynamic": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-static\\config\\config.default.js", "preload": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-static\\config\\config.default.js", "buffer": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-static\\config\\config.default.js", "maxFiles": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-static\\config\\config.default.js"}, "view": {"root": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-view\\config\\config.default.js", "cache": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-view\\config\\config.local.js", "defaultExtension": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-view\\config\\config.default.js", "defaultViewEngine": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-view\\config\\config.default.js", "mapping": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-view\\config\\config.default.js"}, "validate": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\node_modules\\egg-validate\\config\\config.default.js", "mysql": {"default": {"database": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-mysql\\config\\config.default.js", "connectionLimit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-mysql\\config\\config.default.js"}, "app": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-mysql\\config\\config.default.js", "agent": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-mysql\\config\\config.default.js"}, "env": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "cookies": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "proxy": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "maxIpsCount": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "maxProxyCount": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "protocolHeaders": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "ipHeaders": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "hostHeaders": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "pkg": {"name": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "version": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "description": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "private": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg": {"declarations": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "dependencies": {"apollo-server-express": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-cors": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-jwt": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-mysql": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-rate-limiter": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-redis": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-scripts": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-security": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-sequelize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "express-rate-limit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "graphql": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "graphql-tools": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "helmet": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "iconv-lite": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "mysql2": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "rate-limit-redis": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "redis": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "sequelize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "xss": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "devDependencies": {"egg-bin": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-ci": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "egg-mock": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "eslint": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "eslint-config-egg": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "sequelize-cli": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "engines": {"node": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "scripts": {"start": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "stop": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "dev": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "dev:win": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "dev-no-migrate": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "debug": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "test": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "test-local": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "cov": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "lint": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "ci": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "migrate": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "migrate:undo": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "migrate:status": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "test:db-optimization": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "db:optimize": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "ci": {"version": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "type": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "repository": {"type": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "url": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "author": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "license": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "baseDir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "HOME": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "rundir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "dump": {"ignore": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "timing": {"slowBootActionMinDuration": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}}, "confusedConfigurations": {"bodyparser": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "notFound": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "sitefile": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "middlewares": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "httpClient": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "notfound": {"pageUrl": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "siteFile": {"/favicon.ico": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "cacheControl": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "bodyParser": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "encoding": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "formLimit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "jsonLimit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "textLimit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "strict": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "queryString": {"arrayLimit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "depth": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "parameterLimit": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "onerror": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "httpclient": {"enableDNSCache": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "dnsCacheLookupInterval": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "dnsCacheMaxLength": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "request": {"timeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "httpAgent": {"keepAlive": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "freeSocketTimeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "maxSockets": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "maxFreeSockets": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "httpsAgent": {"keepAlive": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "freeSocketTimeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "maxSockets": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "maxFreeSockets": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "useHttpClientNext": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "meta": {"enable": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "logging": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "coreMiddleware": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "workerStartTimeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "serverTimeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "clusterClient": {"maxWaitTime": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js", "responseTimeout": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}, "onClientError": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\config.default.js"}