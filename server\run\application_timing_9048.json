[{"name": "Process Start", "start": 1753493843367, "end": 1753493845085, "duration": 1718, "pid": 9048, "index": 0}, {"name": "Application Start", "start": 1753493845087, "end": 1753493847635, "duration": 2548, "pid": 9048, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753493845106, "end": 1753493845142, "duration": 36, "pid": 9048, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753493845142, "end": 1753493845196, "duration": 54, "pid": 9048, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753493845144, "end": 1753493845145, "duration": 1, "pid": 9048, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753493845149, "end": 1753493845150, "duration": 1, "pid": 9048, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753493845151, "end": 1753493845152, "duration": 1, "pid": 9048, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753493845152, "end": 1753493845153, "duration": 1, "pid": 9048, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753493845154, "end": 1753493845155, "duration": 1, "pid": 9048, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753493845156, "end": 1753493845156, "duration": 0, "pid": 9048, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753493845157, "end": 1753493845157, "duration": 0, "pid": 9048, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753493845158, "end": 1753493845159, "duration": 1, "pid": 9048, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753493845161, "end": 1753493845162, "duration": 1, "pid": 9048, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753493845163, "end": 1753493845164, "duration": 1, "pid": 9048, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753493845165, "end": 1753493845165, "duration": 0, "pid": 9048, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753493845166, "end": 1753493845167, "duration": 1, "pid": 9048, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753493845168, "end": 1753493845168, "duration": 0, "pid": 9048, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753493845169, "end": 1753493845169, "duration": 0, "pid": 9048, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753493845171, "end": 1753493845171, "duration": 0, "pid": 9048, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753493845172, "end": 1753493845173, "duration": 1, "pid": 9048, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753493845173, "end": 1753493845174, "duration": 1, "pid": 9048, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753493845175, "end": 1753493845176, "duration": 1, "pid": 9048, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753493845177, "end": 1753493845178, "duration": 1, "pid": 9048, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753493845179, "end": 1753493845180, "duration": 1, "pid": 9048, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753493845181, "end": 1753493845181, "duration": 0, "pid": 9048, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753493845183, "end": 1753493845184, "duration": 1, "pid": 9048, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753493845186, "end": 1753493845186, "duration": 0, "pid": 9048, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753493845190, "end": 1753493845190, "duration": 0, "pid": 9048, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753493845194, "end": 1753493845195, "duration": 1, "pid": 9048, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753493845195, "end": 1753493845196, "duration": 1, "pid": 9048, "index": 29}, {"name": "Load extend/application.js", "start": 1753493845196, "end": 1753493845316, "duration": 120, "pid": 9048, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753493845197, "end": 1753493845200, "duration": 3, "pid": 9048, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753493845201, "end": 1753493845203, "duration": 2, "pid": 9048, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753493845204, "end": 1753493845212, "duration": 8, "pid": 9048, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753493845215, "end": 1753493845221, "duration": 6, "pid": 9048, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753493845223, "end": 1753493845226, "duration": 3, "pid": 9048, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753493845228, "end": 1753493845231, "duration": 3, "pid": 9048, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753493845232, "end": 1753493845300, "duration": 68, "pid": 9048, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753493845303, "end": 1753493845308, "duration": 5, "pid": 9048, "index": 38}, {"name": "Load extend/request.js", "start": 1753493845316, "end": 1753493845338, "duration": 22, "pid": 9048, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753493845326, "end": 1753493845328, "duration": 2, "pid": 9048, "index": 40}, {"name": "Load extend/response.js", "start": 1753493845338, "end": 1753493845362, "duration": 24, "pid": 9048, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753493845347, "end": 1753493845351, "duration": 4, "pid": 9048, "index": 42}, {"name": "Load extend/context.js", "start": 1753493845362, "end": 1753493845436, "duration": 74, "pid": 9048, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753493845364, "end": 1753493845380, "duration": 16, "pid": 9048, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753493845380, "end": 1753493845383, "duration": 3, "pid": 9048, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753493845384, "end": 1753493845385, "duration": 1, "pid": 9048, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753493845386, "end": 1753493845413, "duration": 27, "pid": 9048, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753493845415, "end": 1753493845417, "duration": 2, "pid": 9048, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753493845419, "end": 1753493845420, "duration": 1, "pid": 9048, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753493845421, "end": 1753493845424, "duration": 3, "pid": 9048, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753493845425, "end": 1753493845427, "duration": 2, "pid": 9048, "index": 51}, {"name": "Load extend/helper.js", "start": 1753493845436, "end": 1753493845491, "duration": 55, "pid": 9048, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753493845437, "end": 1753493845468, "duration": 31, "pid": 9048, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753493845476, "end": 1753493845478, "duration": 2, "pid": 9048, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753493845478, "end": 1753493845479, "duration": 1, "pid": 9048, "index": 55}, {"name": "Load app.js", "start": 1753493845491, "end": 1753493845595, "duration": 104, "pid": 9048, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753493845493, "end": 1753493845494, "duration": 1, "pid": 9048, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753493845495, "end": 1753493845500, "duration": 5, "pid": 9048, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753493845502, "end": 1753493845521, "duration": 19, "pid": 9048, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753493845522, "end": 1753493845544, "duration": 22, "pid": 9048, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753493845545, "end": 1753493845559, "duration": 14, "pid": 9048, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753493845559, "end": 1753493845561, "duration": 2, "pid": 9048, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753493845562, "end": 1753493845565, "duration": 3, "pid": 9048, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753493845565, "end": 1753493845566, "duration": 1, "pid": 9048, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753493845566, "end": 1753493845567, "duration": 1, "pid": 9048, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753493845567, "end": 1753493845568, "duration": 1, "pid": 9048, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753493845570, "end": 1753493845570, "duration": 0, "pid": 9048, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753493845571, "end": 1753493845571, "duration": 0, "pid": 9048, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753493845572, "end": 1753493845573, "duration": 1, "pid": 9048, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753493845573, "end": 1753493845576, "duration": 3, "pid": 9048, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753493845577, "end": 1753493845593, "duration": 16, "pid": 9048, "index": 71}, {"name": "Require(62) app.js", "start": 1753493845595, "end": 1753493845595, "duration": 0, "pid": 9048, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753493845606, "end": 1753493847619, "duration": 2013, "pid": 9048, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753493846307, "end": 1753493846436, "duration": 129, "pid": 9048, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753493846345, "end": 1753493847560, "duration": 1215, "pid": 9048, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753493846491, "end": 1753493847633, "duration": 1142, "pid": 9048, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753493846492, "end": 1753493847228, "duration": 736, "pid": 9048, "index": 77}, {"name": "Load Service", "start": 1753493846492, "end": 1753493846714, "duration": 222, "pid": 9048, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753493846492, "end": 1753493846714, "duration": 222, "pid": 9048, "index": 79}, {"name": "Load Middleware", "start": 1753493846715, "end": 1753493846970, "duration": 255, "pid": 9048, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753493846715, "end": 1753493846949, "duration": 234, "pid": 9048, "index": 81}, {"name": "Load Controller", "start": 1753493846970, "end": 1753493847147, "duration": 177, "pid": 9048, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753493846970, "end": 1753493847147, "duration": 177, "pid": 9048, "index": 83}, {"name": "Load Router", "start": 1753493847147, "end": 1753493847180, "duration": 33, "pid": 9048, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753493847148, "end": 1753493847152, "duration": 4, "pid": 9048, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753493847154, "end": 1753493847225, "duration": 71, "pid": 9048, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753493847633, "end": 1753493847633, "duration": 0, "pid": 9048, "index": 87}]