'use strict';

const Service = require('egg').Service;

/**
 * 智能股票推荐服务
 * 基于技术分析、基本面分析和机器学习的股票推荐算法
 */
class SmartRecommendationService extends Service {

  /**
   * 获取智能推荐股票列表
   * @param {Object} options - 推荐选项
   * @param {string} options.riskLevel - 风险等级 (low/medium/high)
   * @param {number} options.expectedReturn - 预期收益率
   * @param {number} options.timeHorizon - 投资时间范围（天）
   * @param {number} options.limit - 推荐数量限制
   * @param {string} options.industry - 行业筛选
   * @param {string} options.market - 板块筛选
   * @param {string} options.marketCap - 市值筛选 (large/medium/small)
   * @return {Array} 推荐股票列表
   */
  async getRecommendations(options = {}) {
    const { ctx } = this;
    const {
      riskLevel = 'medium',
      expectedReturn = 0.05,
      timeHorizon = 7,
      limit = 10,
      industry = null,
      market = null,
      marketCap = null
    } = options;

    try {
      // 1. 获取股票池（支持筛选条件）
      const stockPool = await this.getStockPool({
        industry,
        market,
        marketCap,
        riskLevel,
        timeHorizon
      });

      // 2. 对每只股票进行评分
      const scoredStocks = [];
      for (const stock of stockPool) {
        try {
          const score = await this.calculateStockScore(stock, {
            riskLevel,
            expectedReturn,
            timeHorizon
          });

          if (score && score.totalScore > 30) { // 进一步降低推荐门槛到30分
            ctx.logger.info(`✅ 股票 ${stock.symbol} 通过筛选，评分: ${score.totalScore}`);
            scoredStocks.push({
              ...stock,
              ...score
            });
          } else {
            ctx.logger.warn(`❌ 股票 ${stock.symbol} 未通过筛选，评分: ${score ? score.totalScore : 'null'}`);
          }
        } catch (error) {
          ctx.logger.warn(`计算股票 ${stock.symbol} 评分失败:`, error);
        }
      }

      // 3. 按评分排序并限制数量
      const recommendations = scoredStocks
        .sort((a, b) => b.totalScore - a.totalScore)
        .slice(0, limit);

      // 如果没有推荐结果，返回空数组而不是生成模拟数据
      let enrichedRecommendations = [];
      if (recommendations.length === 0) {
        ctx.logger.warn('没有符合条件的推荐，无法提供推荐结果');
        enrichedRecommendations = [];
      } else {
        // 4. 生成推荐理由和买卖建议
        enrichedRecommendations = await Promise.all(
          recommendations.map(stock => this.enrichRecommendation(stock))
        );
      }

      // 5. 保存推荐记录
      await this.saveRecommendationRecord(enrichedRecommendations, options);

      return {
        success: true,
        data: enrichedRecommendations,
        meta: {
          totalAnalyzed: stockPool.length,
          qualified: scoredStocks.length,
          recommended: enrichedRecommendations.length,
          generatedAt: new Date(),
          criteria: options
        }
      };

    } catch (error) {
      ctx.logger.error('获取智能推荐失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 获取股票池
   * 筛选出适合分析的活跃股票
   * @param {Object} filters - 筛选条件
   * @param {string} filters.industry - 行业筛选
   * @param {string} filters.market - 板块筛选
   * @param {string} filters.marketCap - 市值筛选
   * @param {string} filters.riskLevel - 风险等级
   * @param {number} filters.timeHorizon - 投资时间范围
   */
  async getStockPool(filters = {}) {
    const { ctx, app } = this;
    const { industry, market, marketCap, riskLevel, timeHorizon } = filters;

    try {
      ctx.logger.info('🔍 开始从数据库获取股票池...', { filters });

      // 构建动态SQL查询 - 简化条件，确保能获取到数据
      let whereConditions = ['1=1']; // 移除list_status限制，确保能获取到数据
      let queryParams = [];

      // 行业筛选
      if (industry && industry !== 'all') {
        whereConditions.push('industry LIKE ?');
        queryParams.push(`%${industry}%`);
      }

      // 板块筛选（基于股票代码前缀）
      if (market && market !== 'all') {
        const marketConditions = this.getMarketConditions(market);
        if (marketConditions) {
          whereConditions.push(marketConditions);
        }
      }

      // 市值筛选（基于股票代码规律）
      if (marketCap && marketCap !== 'all') {
        const marketCapConditions = this.getMarketCapConditions(marketCap);
        if (marketCapConditions) {
          whereConditions.push(marketCapConditions);
        }
      }

      // 根据风险等级和投资期限调整股票池大小
      let limitSize = 200; // 默认200只股票
      if (riskLevel === 'low') {
        limitSize = 150; // 低风险选择更少但更稳定的股票
      } else if (riskLevel === 'high') {
        limitSize = 300; // 高风险可以选择更多股票
      }

      if (timeHorizon <= 7) {
        limitSize = Math.min(limitSize, 100); // 短期投资减少选择范围
      }

      const rawQuery = `
        SELECT ts_code as tsCode, symbol, name, area, industry, market, list_date as listDate
        FROM stock_basic
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY ${this.getOrderByClause(riskLevel, timeHorizon)}
        LIMIT ${limitSize}
      `;

      ctx.logger.info('📊 执行SQL查询:', rawQuery);
      ctx.logger.info('📊 查询参数:', queryParams);

      const [results] = await app.model.query(rawQuery, {
        type: app.model.QueryTypes.SELECT,
        replacements: queryParams
      });

      ctx.logger.info(`📈 数据库查询结果: ${results ? results.length : 0} 条记录`);

      let allStocks = [];
      if (results && results.length > 0) {
        allStocks = results;
        ctx.logger.info(`✅ 从数据库获取到 ${allStocks.length} 只股票`);
        ctx.logger.info('📋 前3只股票样本:', allStocks.slice(0, 3));
      } else {
        ctx.logger.error('❌ 数据库查询返回空结果！');
        ctx.logger.info('🔍 开始诊断数据库问题...');

        try {
          // 检查数据库连接和表结构
          const [tableCheck] = await app.model.query('SHOW TABLES LIKE "stock_basic"', {
            type: app.model.QueryTypes.SELECT
          });

          if (tableCheck.length === 0) {
            ctx.logger.error('❌ stock_basic 表不存在！');
            throw new Error('stock_basic 表不存在');
          }

          ctx.logger.info('✅ stock_basic 表存在');

          // 检查表中的数据量
          const [countCheck] = await app.model.query('SELECT COUNT(*) as count FROM stock_basic', {
            type: app.model.QueryTypes.SELECT
          });

          const totalCount = countCheck[0].count;
          ctx.logger.info(`📊 stock_basic 表中共有 ${totalCount} 条记录`);

          if (totalCount === 0) {
            ctx.logger.error('❌ stock_basic 表为空！需要导入股票数据');
            throw new Error('stock_basic 表为空，需要导入股票数据');
          }

          // 检查 list_status 字段的分布
          const [statusCheck] = await app.model.query('SELECT list_status, COUNT(*) as count FROM stock_basic GROUP BY list_status', {
            type: app.model.QueryTypes.SELECT
          });

          ctx.logger.info('📈 list_status 分布:');
          statusCheck.forEach(row => {
            ctx.logger.info(`  ${row.list_status || 'NULL'}: ${row.count} 条`);
          });

          // 重新查询，不使用 list_status 过滤
          ctx.logger.info('🔄 重新查询所有股票数据...');
          const [allResults] = await app.model.query('SELECT ts_code, symbol, name, area, industry, market, list_date FROM stock_basic LIMIT 500', {
            type: app.model.QueryTypes.SELECT
          });

          if (allResults && allResults.length > 0) {
            allStocks = allResults.map(row => ({
              symbol: row.symbol || row.ts_code,
              tsCode: row.ts_code,
              name: row.name,
              area: row.area,
              industry: row.industry,
              market: row.market,
              listDate: row.list_date
            }));
            ctx.logger.info(`✅ 成功获取 ${allStocks.length} 只股票数据`);
          } else {
            throw new Error('重新查询仍然返回空结果');
          }

        } catch (dbError) {
          ctx.logger.error('❌ 数据库诊断失败:', dbError.message);
          ctx.logger.warn('🔄 回退到股票服务获取数据...');

          // 回退到股票服务
          try {
            const stocksResult = await ctx.service.stock.getStockList();
            if (stocksResult && stocksResult.data && Array.isArray(stocksResult.data) && stocksResult.data.length > 0) {
              allStocks = stocksResult.data;
              ctx.logger.warn(`⚠️ 使用股票服务获取到 ${allStocks.length} 只股票（非数据库数据）`);
            } else {
              throw new Error('股票服务返回空数据');
            }
          } catch (stockServiceError) {
            ctx.logger.warn('❌ 股票服务也无法获取数据，使用基础股票列表');
            ctx.logger.warn('股票服务错误:', stockServiceError.message);
            allStocks = this.getBasicStockList();
          }
        }
      }

      ctx.logger.info(`获取到 ${allStocks.length} 条股票数据用于推荐分析`);

      // 如果没有股票数据，返回空数组
      if (!allStocks || allStocks.length === 0) {
        ctx.logger.warn('没有可用的股票数据进行推荐分析');
        return [];
      }

      // 筛选条件：
      // 1. 上市时间超过1年
      // 2. 非ST股票
      // 3. 市值适中（避免过小的股票）
      ctx.logger.info(`开始筛选股票，总数: ${allStocks.length}`);

      const filteredStocks = allStocks.filter(stock => {
        try {
          // 处理不同的日期字段名
          const listDateStr = stock.listDate || stock.list_date || stock.list_dt;

          // 解析上市日期
          let listDate;
          let dateCheck = true;
          if (listDateStr) {
            if (typeof listDateStr === 'string') {
              // 处理不同的日期格式：20200101 或 2020-01-01
              const dateStr = listDateStr.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
              listDate = new Date(dateStr);
            } else {
              listDate = new Date(listDateStr);
            }

            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
            dateCheck = listDate < oneYearAgo;
          }

          // 获取股票代码和名称
          const symbol = stock.symbol || stock.tsCode || '';
          const name = stock.name || '';

          // 检查各个条件
          const stCheck = !name.includes('ST');
          const starCheck = !name.includes('*');
          const symbolCheck = symbol.match(/^(000|002|300|600|601|603|688)/);

          const passed = dateCheck && stCheck && starCheck && symbolCheck;

          ctx.logger.debug(`股票 ${symbol} (${name}) 筛选结果: ${passed ? '通过' : '未通过'} - 日期:${dateCheck}, ST:${stCheck}, 星号:${starCheck}, 代码:${!!symbolCheck}`);

          return passed;
        } catch (error) {
          ctx.logger.warn(`筛选股票 ${stock.symbol || stock.tsCode} 时出错:`, error);
          return false; // 出错的股票不包含在推荐池中
        }
      });

      ctx.logger.info(`筛选后股票数量: ${filteredStocks.length}`);

      // 如果筛选后没有股票，返回空数组而不是模拟数据
      if (filteredStocks.length === 0) {
        ctx.logger.warn('筛选后没有股票，无法提供推荐');
        return [];
      }

      // 随机选择一部分股票进行分析（避免计算量过大）
      const sampleSize = Math.min(100, filteredStocks.length);
      const shuffled = filteredStocks.sort(() => 0.5 - Math.random());

      return shuffled.slice(0, sampleSize);

    } catch (error) {
      ctx.logger.error('获取股票池失败:', error);
      return [];
    }
  }

  /**
   * 获取真实的股票价格
   * @param {string} symbol - 股票代码
   * @return {number} 股票价格
   */
  async getRealStockPrice(symbol) {
    const { ctx } = this;

    try {
      // 尝试多个数据源获取真实价格（按优先级排序）
      const dataSources = ['sina', 'eastmoney', 'alphavantage', 'alltick'];

      for (const source of dataSources) {
        try {
          ctx.logger.debug(`尝试从 ${source} 获取 ${symbol} 价格`);

          // 这里可以调用不同的数据源API
          // 由于当前系统的限制，我们使用一个简化的方法
          const price = await this.fetchPriceFromAlternativeSource(symbol, source);
          if (price && price > 0 && price !== 100) {
            ctx.logger.info(`从 ${source} 获取到 ${symbol} 真实价格: ${price}`);
            return price;
          }
        } catch (error) {
          ctx.logger.warn(`从 ${source} 获取 ${symbol} 价格失败:`, error.message);
          continue;
        }
      }

      // 如果所有数据源都失败，抛出错误而不是返回模拟数据
      throw new Error(`无法从任何真实数据源获取 ${symbol} 的价格数据`);
    } catch (error) {
      ctx.logger.error(`获取 ${symbol} 真实价格失败:`, error);
      return null;
    }
  }

  /**
   * 从备用数据源获取价格
   * @param {string} symbol - 股票代码
   * @param {string} source - 数据源名称
   * @return {number} 股票价格
   */
  async fetchPriceFromAlternativeSource(symbol, source) {
    const { ctx } = this;

    try {
      ctx.logger.info(`尝试从 ${source} 数据源获取 ${symbol} 真实价格`);

      // 调用不同的数据源API
      switch (source) {
        case 'sina':
          return await this.fetchFromSinaAPI(symbol);
        case 'eastmoney':
          return await this.fetchFromEastMoneyAPI(symbol);
        case 'alphavantage':
          return await this.fetchFromAlphaVantageAPI(symbol);
        case 'alltick':
          return await this.fetchFromAlltickAPI(symbol);
        default:
          ctx.logger.warn(`未知数据源: ${source}`);
          return null;
      }
    } catch (error) {
      ctx.logger.warn(`从 ${source} 获取 ${symbol} 价格失败:`, error.message);
      return null;
    }
  }

  /**
   * 从新浪财经API获取价格
   */
  async fetchFromSinaAPI(symbol) {
    const { ctx } = this;
    try {
      // 调用新浪财经API
      const response = await ctx.curl(`http://localhost:7001/api/sina/quote?symbol=${symbol}`, {
        method: 'GET',
        timeout: 10000,
        dataType: 'json'
      });

      if (response.data && response.data.success && response.data.data && response.data.data.price) {
        const price = parseFloat(response.data.data.price);
        if (price > 0 && price !== 100) {
          ctx.logger.info(`从新浪财经获取到 ${symbol} 真实价格: ${price}`);
          return price;
        }
      }
      return null;
    } catch (error) {
      ctx.logger.warn(`新浪财经API调用失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 从东方财富API获取价格
   */
  async fetchFromEastMoneyAPI(symbol) {
    const { ctx } = this;
    try {
      // 调用东方财富API
      const response = await ctx.curl(`http://localhost:7001/api/eastmoney/quote?symbol=${symbol}`, {
        method: 'GET',
        timeout: 10000,
        dataType: 'json'
      });

      if (response.data && response.data.success && response.data.data && response.data.data.price) {
        const price = parseFloat(response.data.data.price);
        if (price > 0 && price !== 100) {
          ctx.logger.info(`从东方财富获取到 ${symbol} 真实价格: ${price}`);
          return price;
        }
      }
      return null;
    } catch (error) {
      ctx.logger.warn(`东方财富API调用失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 从Alpha Vantage API获取价格
   */
  async fetchFromAlphaVantageAPI(symbol) {
    const { ctx } = this;
    try {
      // 调用Alpha Vantage API
      const response = await ctx.curl(`http://localhost:7001/api/alphavantage/quote?symbol=${symbol}`, {
        method: 'GET',
        timeout: 15000,
        dataType: 'json'
      });

      if (response.data && response.data.success && response.data.data && response.data.data.price) {
        const price = parseFloat(response.data.data.price);
        if (price > 0 && price !== 100) {
          ctx.logger.info(`从Alpha Vantage获取到 ${symbol} 真实价格: ${price}`);
          return price;
        }
      }
      return null;
    } catch (error) {
      ctx.logger.warn(`Alpha Vantage API调用失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 从AllTick API获取价格
   */
  async fetchFromAlltickAPI(symbol) {
    const { ctx } = this;
    try {
      // 调用AllTick API
      const response = await ctx.curl(`http://localhost:7001/api/alltick/quote?symbol=${symbol}`, {
        method: 'GET',
        timeout: 15000,
        dataType: 'json'
      });

      if (response.data && response.data.success && response.data.data && response.data.data.price) {
        const price = parseFloat(response.data.data.price);
        if (price > 0 && price !== 100) {
          ctx.logger.info(`从AllTick获取到 ${symbol} 真实价格: ${price}`);
          return price;
        }
      }
      return null;
    } catch (error) {
      ctx.logger.warn(`AllTick API调用失败: ${error.message}`);
      return null;
    }
  }

  // 已删除 generateReasonablePrice 方法 - 禁止使用模拟数据

  /**
   * 计算股票综合评分
   * @param {Object} stock - 股票基本信息
   * @param {Object} options - 评分选项
   * @return {Object} 评分结果
   */
  async calculateStockScore(stock, options) {
    const { ctx } = this;

    try {
      // 获取股票历史数据（最近60个交易日）
      const symbol = stock.symbol || stock.tsCode;
      ctx.logger.info(`📊 开始分析股票: ${symbol} (${stock.name})`);

      const historicalDataResult = await ctx.service.stock.getStockHistory(
        symbol,
        this.getDateString(-60),
        this.getDateString(0)
      );

      ctx.logger.info(`📈 ${symbol} 历史数据查询结果:`, {
        hasResult: !!historicalDataResult,
        hasData: !!(historicalDataResult && historicalDataResult.data),
        dataLength: historicalDataResult && historicalDataResult.data ? historicalDataResult.data.length : 0,
        isArray: Array.isArray(historicalDataResult)
      });

      // 处理返回的数据格式
      let historicalData = [];
      if (historicalDataResult && historicalDataResult.data && Array.isArray(historicalDataResult.data)) {
        historicalData = historicalDataResult.data;
      } else if (Array.isArray(historicalDataResult)) {
        historicalData = historicalDataResult;
      }

      ctx.logger.info(`📊 ${symbol} 处理后历史数据长度: ${historicalData.length}`);

      if (!historicalData || historicalData.length < 5) {
        ctx.logger.warn(`❌ 股票 ${symbol} 历史数据不足 (${historicalData.length} < 5)，使用基础评分`);
        // 如果历史数据不足，使用基础评分而不是跳过
        return this.calculateBasicScore(stock, options);
      }

      ctx.logger.info(`✅ 股票 ${symbol} 历史数据充足，开始评分计算`);

      // 尝试计算各项评分，如果失败则使用简化算法
      let technicalScore, volumePriceScore, trendScore, momentumScore;

      try {
        // 1. 技术分析评分 (40%)
        technicalScore = await this.calculateTechnicalScore(historicalData);
      } catch (error) {
        ctx.logger.warn(`技术分析评分计算失败，使用简化算法: ${error.message}`);
        try {
          technicalScore = this.calculateSimpleTechnicalScore(historicalData);
        } catch (simpleError) {
          ctx.logger.warn(`简化技术分析也失败，使用默认评分: ${simpleError.message}`);
          technicalScore = 60; // 默认评分
        }
      }

      try {
        // 2. 量价分析评分 (30%)
        volumePriceScore = await this.calculateVolumePriceScore(historicalData);
      } catch (error) {
        ctx.logger.warn(`量价分析评分计算失败，使用简化算法: ${error.message}`);
        try {
          volumePriceScore = this.calculateSimpleVolumeScore(historicalData);
        } catch (simpleError) {
          ctx.logger.warn(`简化量价分析也失败，使用默认评分: ${simpleError.message}`);
          volumePriceScore = 55; // 默认评分
        }
      }

      try {
        // 3. 趋势分析评分 (20%)
        trendScore = await this.calculateTrendScore(historicalData);
      } catch (error) {
        ctx.logger.warn(`趋势分析评分计算失败，使用简化算法: ${error.message}`);
        try {
          trendScore = this.calculateSimpleTrendScore(historicalData);
        } catch (simpleError) {
          ctx.logger.warn(`简化趋势分析也失败，使用默认评分: ${simpleError.message}`);
          trendScore = 58; // 默认评分
        }
      }

      try {
        // 4. 动量分析评分 (10%)
        momentumScore = await this.calculateMomentumScore(historicalData);
      } catch (error) {
        ctx.logger.warn(`动量分析评分计算失败，使用简化算法: ${error.message}`);
        try {
          momentumScore = this.calculateSimpleMomentumScore(historicalData);
        } catch (simpleError) {
          ctx.logger.warn(`简化动量分析也失败，使用默认评分: ${simpleError.message}`);
          momentumScore = 52; // 默认评分
        }
      }

      // 计算基础综合评分
      let totalScore = (
        technicalScore * 0.4 +
        volumePriceScore * 0.3 +
        trendScore * 0.2 +
        momentumScore * 0.1
      );

      // 根据用户偏好调整评分
      totalScore = this.adjustScoreByUserPreference(totalScore, stock, options);

      ctx.logger.info(`📊 股票 ${stock.symbol} 各项评分: 技术=${technicalScore}, 量价=${volumePriceScore}, 趋势=${trendScore}, 动量=${momentumScore}, 基础=${Math.round(totalScore - this.getPreferenceAdjustment(stock, options))}, 调整后=${Math.round(totalScore)}`);

      // 计算风险等级
      const riskLevel = this.calculateRiskLevel(historicalData, totalScore);

      // 计算预期收益率
      const expectedReturn = this.calculateExpectedReturn(historicalData, totalScore);

      const result = {
        totalScore: Math.round(totalScore),
        technicalScore: Math.round(technicalScore),
        volumePriceScore: Math.round(volumePriceScore),
        trendScore: Math.round(trendScore),
        momentumScore: Math.round(momentumScore),
        riskLevel,
        expectedReturn,
        currentPrice: historicalData[historicalData.length - 1].close,
        dataPoints: historicalData.length
      };

      ctx.logger.info(`✅ 股票 ${stock.symbol} 评分计算完成: ${result.totalScore}分`);
      return result;

    } catch (error) {
      ctx.logger.error(`计算股票 ${stock.symbol} 评分失败:`, error);
      return null;
    }
  }

  /**
   * 计算技术分析评分
   * @param {Array} data - 历史数据
   * @return {number} 技术分析评分 (0-100)
   */
  async calculateTechnicalScore(data) {
    const { ctx } = this;

    try {
      // 计算技术指标
      const indicators = await ctx.service.technicalIndicators.calculateIndicators(data, {
        sma: { enabled: true, periods: [5, 10, 20] },
        ema: { enabled: true, periods: [12, 26] },
        macd: { enabled: true },
        rsi: { enabled: true, period: 14 },
        kdj: { enabled: true },
        bollingerBands: { enabled: true, period: 20 }
      });

      let score = 0;
      let factors = 0;

      // 1. 移动平均线分析 (25分)
      if (indicators.sma) {
        const currentPrice = data[data.length - 1].close;
        const sma5 = indicators.sma.sma5[indicators.sma.sma5.length - 1];
        const sma10 = indicators.sma.sma10[indicators.sma.sma10.length - 1];
        const sma20 = indicators.sma.sma20[indicators.sma.sma20.length - 1];

        // 多头排列：价格 > 5日线 > 10日线 > 20日线
        if (currentPrice > sma5 && sma5 > sma10 && sma10 > sma20) {
          score += 25;
        } else if (currentPrice > sma5 && sma5 > sma10) {
          score += 15;
        } else if (currentPrice > sma5) {
          score += 10;
        }
        factors++;
      }

      // 2. MACD分析 (20分)
      if (indicators.macd) {
        const macdData = indicators.macd;
        const currentMACD = macdData.macd[macdData.macd.length - 1];
        const currentSignal = macdData.signal[macdData.signal.length - 1];
        const currentHist = macdData.histogram[macdData.histogram.length - 1];
        const prevHist = macdData.histogram[macdData.histogram.length - 2];

        // MACD金叉且柱状图向上
        if (currentMACD > currentSignal && currentHist > prevHist && currentHist > 0) {
          score += 20;
        } else if (currentMACD > currentSignal) {
          score += 15;
        } else if (currentHist > prevHist) {
          score += 10;
        }
        factors++;
      }

      // 3. RSI分析 (20分)
      if (indicators.rsi) {
        const currentRSI = indicators.rsi[indicators.rsi.length - 1];

        // RSI在30-70之间，且呈上升趋势
        if (currentRSI >= 30 && currentRSI <= 70) {
          const prevRSI = indicators.rsi[indicators.rsi.length - 2];
          if (currentRSI > prevRSI) {
            score += 20;
          } else {
            score += 15;
          }
        } else if (currentRSI > 70) {
          score += 5; // 超买区域，降低评分
        } else {
          score += 10; // 超卖区域，适度加分
        }
        factors++;
      }

      // 4. KDJ分析 (15分)
      if (indicators.kdj) {
        const currentK = indicators.kdj.k[indicators.kdj.k.length - 1];
        const currentD = indicators.kdj.d[indicators.kdj.d.length - 1];
        const currentJ = indicators.kdj.j[indicators.kdj.j.length - 1];

        // KDJ金叉且在低位
        if (currentK > currentD && currentK < 80 && currentD < 80) {
          score += 15;
        } else if (currentK > currentD) {
          score += 10;
        } else if (currentK < 20 && currentD < 20) {
          score += 8; // 超卖区域
        }
        factors++;
      }

      // 5. 布林带分析 (20分)
      if (indicators.bollingerBands) {
        const currentPrice = data[data.length - 1].close;
        const upperBand = indicators.bollingerBands.upper[indicators.bollingerBands.upper.length - 1];
        const middleBand = indicators.bollingerBands.middle[indicators.bollingerBands.middle.length - 1];
        const lowerBand = indicators.bollingerBands.lower[indicators.bollingerBands.lower.length - 1];

        // 价格位置分析
        const position = (currentPrice - lowerBand) / (upperBand - lowerBand);

        if (position >= 0.2 && position <= 0.8 && currentPrice > middleBand) {
          score += 20; // 在中上轨之间，较为安全
        } else if (currentPrice > middleBand) {
          score += 15;
        } else if (position < 0.2) {
          score += 12; // 接近下轨，可能反弹
        }
        factors++;
      }

      // 计算平均分
      return factors > 0 ? score / factors * (100 / 100) : 0;

    } catch (error) {
      this.ctx.logger.error('计算技术分析评分失败:', error);
      return 0;
    }
  }

  /**
   * 计算量价分析评分
   * @param {Array} data - 历史数据
   * @return {number} 量价分析评分 (0-100)
   */
  async calculateVolumePriceScore(data) {
    try {
      let score = 0;
      const recentDays = 10;
      const recent = data.slice(-recentDays);

      // 1. 量价配合度分析 (40分)
      let volumePriceMatch = 0;
      for (let i = 1; i < recent.length; i++) {
        const priceChange = recent[i].close - recent[i - 1].close;
        const volumeChange = recent[i].volume - recent[i - 1].volume;

        // 价涨量增或价跌量减为正向配合
        if ((priceChange > 0 && volumeChange > 0) || (priceChange < 0 && volumeChange < 0)) {
          volumePriceMatch++;
        }
      }
      score += (volumePriceMatch / (recent.length - 1)) * 40;

      // 2. 成交量趋势分析 (30分)
      const avgVolume = recent.reduce((sum, item) => sum + item.volume, 0) / recent.length;
      const recentVolume = recent.slice(-3).reduce((sum, item) => sum + item.volume, 0) / 3;

      if (recentVolume > avgVolume * 1.2) {
        score += 30; // 成交量放大
      } else if (recentVolume > avgVolume) {
        score += 20;
      } else {
        score += 10;
      }

      // 3. 价格突破分析 (30分)
      const currentPrice = recent[recent.length - 1].close;
      const highestPrice = Math.max(...recent.slice(0, -1).map(item => item.high));
      const lowestPrice = Math.min(...recent.slice(0, -1).map(item => item.low));

      if (currentPrice > highestPrice) {
        score += 30; // 突破前期高点
      } else if (currentPrice > highestPrice * 0.98) {
        score += 20; // 接近前期高点
      } else if (currentPrice < lowestPrice) {
        score += 5; // 跌破前期低点，风险较高
      } else {
        score += 15;
      }

      return Math.min(100, score);

    } catch (error) {
      this.ctx.logger.error('计算量价分析评分失败:', error);
      return 0;
    }
  }

  /**
   * 计算趋势分析评分
   * @param {Array} data - 历史数据
   * @return {number} 趋势分析评分 (0-100)
   */
  async calculateTrendScore(data) {
    try {
      let score = 0;
      const shortTerm = data.slice(-5); // 短期5天
      const mediumTerm = data.slice(-20); // 中期20天

      // 1. 短期趋势分析 (40分)
      const shortTrendScore = this.calculateTrendDirection(shortTerm);
      score += shortTrendScore * 0.4;

      // 2. 中期趋势分析 (40分)
      const mediumTrendScore = this.calculateTrendDirection(mediumTerm);
      score += mediumTrendScore * 0.4;

      // 3. 趋势一致性分析 (20分)
      if (shortTrendScore > 60 && mediumTrendScore > 60) {
        score += 20; // 短期和中期趋势都向上
      } else if (shortTrendScore > 60 || mediumTrendScore > 60) {
        score += 10; // 至少一个趋势向上
      }

      return Math.min(100, score);

    } catch (error) {
      this.ctx.logger.error('计算趋势分析评分失败:', error);
      return 0;
    }
  }

  /**
   * 计算趋势方向评分
   * @param {Array} data - 数据段
   * @return {number} 趋势评分 (0-100)
   */
  calculateTrendDirection(data) {
    if (data.length < 2) return 50;

    let upDays = 0;
    let totalDays = data.length - 1;

    for (let i = 1; i < data.length; i++) {
      if (data[i].close > data[i - 1].close) {
        upDays++;
      }
    }

    const upRatio = upDays / totalDays;

    // 计算价格变化幅度
    const startPrice = data[0].close;
    const endPrice = data[data.length - 1].close;
    const priceChange = (endPrice - startPrice) / startPrice;

    // 综合上涨天数比例和价格变化幅度
    let score = upRatio * 60; // 基础分数

    if (priceChange > 0.05) {
      score += 40; // 涨幅超过5%，额外加分
    } else if (priceChange > 0.02) {
      score += 25; // 涨幅超过2%
    } else if (priceChange > 0) {
      score += 15; // 小幅上涨
    }

    return Math.min(100, score);
  }

  /**
   * 计算动量分析评分
   * @param {Array} data - 历史数据
   * @return {number} 动量分析评分 (0-100)
   */
  async calculateMomentumScore(data) {
    try {
      let score = 0;

      // 1. 价格动量 (50分)
      const priceChanges = [];
      for (let i = 1; i < data.length; i++) {
        priceChanges.push((data[i].close - data[i - 1].close) / data[i - 1].close);
      }

      const recentChanges = priceChanges.slice(-5); // 最近5天
      const avgRecentChange = recentChanges.reduce((sum, change) => sum + change, 0) / recentChanges.length;

      if (avgRecentChange > 0.02) {
        score += 50; // 强势上涨动量
      } else if (avgRecentChange > 0.01) {
        score += 35; // 中等上涨动量
      } else if (avgRecentChange > 0) {
        score += 25; // 弱势上涨动量
      } else {
        score += 10; // 下跌动量，给予基础分
      }

      // 2. 成交量动量 (30分)
      const volumeChanges = [];
      for (let i = 1; i < data.length; i++) {
        volumeChanges.push((data[i].volume - data[i - 1].volume) / data[i - 1].volume);
      }

      const recentVolumeChanges = volumeChanges.slice(-5);
      const avgVolumeChange = recentVolumeChanges.reduce((sum, change) => sum + change, 0) / recentVolumeChanges.length;

      if (avgVolumeChange > 0.1) {
        score += 30; // 成交量大幅放大
      } else if (avgVolumeChange > 0.05) {
        score += 20; // 成交量适度放大
      } else if (avgVolumeChange > 0) {
        score += 15; // 成交量小幅放大
      } else {
        score += 5; // 成交量萎缩
      }

      // 3. 波动率分析 (20分)
      const volatility = this.calculateVolatility(priceChanges);

      if (volatility > 0.02 && volatility < 0.05) {
        score += 20; // 适度波动，有利于趋势形成
      } else if (volatility <= 0.02) {
        score += 15; // 低波动
      } else {
        score += 10; // 高波动，风险较大
      }

      return Math.min(100, score);

    } catch (error) {
      this.ctx.logger.error('计算动量分析评分失败:', error);
      return 0;
    }
  }

  /**
   * 计算波动率
   * @param {Array} changes - 价格变化数组
   * @return {number} 波动率
   */
  calculateVolatility(changes) {
    if (changes.length === 0) return 0;

    const mean = changes.reduce((sum, change) => sum + change, 0) / changes.length;
    const variance = changes.reduce((sum, change) => sum + Math.pow(change - mean, 2), 0) / changes.length;

    return Math.sqrt(variance);
  }

  /**
   * 计算风险等级
   * @param {Array} data - 历史数据
   * @param {number} score - 综合评分
   * @return {string} 风险等级
   */
  calculateRiskLevel(data, score) {
    try {
      // 计算价格波动率
      const priceChanges = [];
      for (let i = 1; i < data.length; i++) {
        priceChanges.push((data[i].close - data[i - 1].close) / data[i - 1].close);
      }

      const volatility = this.calculateVolatility(priceChanges);

      // 综合评分和波动率确定风险等级
      if (score >= 80 && volatility < 0.03) {
        return 'low';
      } else if (score >= 70 && volatility < 0.05) {
        return 'medium';
      } else if (score >= 60) {
        return 'medium';
      } else {
        return 'high';
      }

    } catch (error) {
      return 'high'; // 出错时返回高风险
    }
  }

  /**
   * 计算预期收益率
   * @param {Array} data - 历史数据
   * @param {number} score - 综合评分
   * @return {number} 预期收益率
   */
  calculateExpectedReturn(data, score) {
    try {
      // 基于评分计算基础收益率
      let baseReturn = (score - 50) / 1000; // 评分每高1分，收益率增加0.1%

      // 基于历史表现调整
      const recentData = data.slice(-10);
      const recentReturn = (recentData[recentData.length - 1].close - recentData[0].close) / recentData[0].close;

      // 综合计算预期收益率
      const expectedReturn = baseReturn * 0.7 + recentReturn * 0.3;

      // 限制在合理范围内
      return Math.max(-0.1, Math.min(0.2, expectedReturn));

    } catch (error) {
      return 0.05; // 默认5%收益率
    }
  }

  /**
   * 增强推荐信息
   * @param {Object} stock - 股票信息
   * @return {Object} 增强后的推荐信息
   */
  async enrichRecommendation(stock) {
    const { ctx } = this;

    try {
      // 生成推荐理由
      const reasons = this.generateRecommendationReasons(stock);

      // 计算买卖建议
      const tradingAdvice = this.generateTradingAdvice(stock);

      // 计算目标价位
      const targetPrice = this.calculateTargetPrice(stock);

      return {
        symbol: stock.symbol,
        name: stock.name,
        currentPrice: stock.currentPrice,
        totalScore: stock.totalScore,
        riskLevel: stock.riskLevel,
        expectedReturn: stock.expectedReturn,
        reasons,
        tradingAdvice,
        targetPrice,
        recommendation: this.getRecommendationLevel(stock.totalScore),
        generatedAt: new Date(),
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天有效期
      };

    } catch (error) {
      ctx.logger.error(`增强推荐信息失败 ${stock.symbol}:`, error);
      return stock;
    }
  }

  /**
   * 生成推荐理由
   * @param {Object} stock - 股票信息
   * @return {Array} 推荐理由列表
   */
  generateRecommendationReasons(stock) {
    const reasons = [];

    // 技术面分析理由
    if (stock.technicalScore >= 80) {
      reasons.push('技术指标表现优异，多项指标显示买入信号');
    } else if (stock.technicalScore >= 70) {
      reasons.push('技术指标整体向好，具备上涨潜力');
    }

    // 量价分析理由
    if (stock.volumePriceScore >= 80) {
      reasons.push('量价配合良好，成交量支撑价格上涨');
    } else if (stock.volumePriceScore >= 70) {
      reasons.push('量价关系健康，资金关注度较高');
    }

    // 趋势分析理由
    if (stock.trendScore >= 80) {
      reasons.push('趋势明确向上，短中期走势强劲');
    } else if (stock.trendScore >= 70) {
      reasons.push('上升趋势初步确立，有望延续');
    }

    // 动量分析理由
    if (stock.momentumScore >= 80) {
      reasons.push('价格动量强劲，上涨势头明显');
    } else if (stock.momentumScore >= 70) {
      reasons.push('动量指标积极，具备继续上涨动力');
    }

    // 风险提示
    if (stock.riskLevel === 'high') {
      reasons.push('⚠️ 注意：该股票波动较大，请控制仓位');
    }

    return reasons.length > 0 ? reasons : ['综合技术指标显示该股票具备投资价值'];
  }

  /**
   * 生成交易建议
   * @param {Object} stock - 股票信息
   * @return {Object} 交易建议
   */
  generateTradingAdvice(stock) {
    const currentPrice = stock.currentPrice;
    const score = stock.totalScore;

    // 基于评分确定买入价格区间
    let buyPriceRange;
    if (score >= 85) {
      // 高分股票，可以适当追高
      buyPriceRange = {
        min: currentPrice * 0.98,
        max: currentPrice * 1.02,
        optimal: currentPrice
      };
    } else if (score >= 75) {
      // 中高分股票，建议回调买入
      buyPriceRange = {
        min: currentPrice * 0.95,
        max: currentPrice * 1.01,
        optimal: currentPrice * 0.98
      };
    } else {
      // 中等分数，建议大幅回调买入
      buyPriceRange = {
        min: currentPrice * 0.92,
        max: currentPrice * 0.98,
        optimal: currentPrice * 0.95
      };
    }

    // 止损建议
    const stopLoss = currentPrice * 0.92; // 8%止损

    // 持有时间建议
    let holdingPeriod;
    if (stock.riskLevel === 'low') {
      holdingPeriod = '5-10个交易日';
    } else if (stock.riskLevel === 'medium') {
      holdingPeriod = '3-7个交易日';
    } else {
      holdingPeriod = '1-5个交易日';
    }

    return {
      buyPriceRange,
      stopLoss,
      holdingPeriod,
      positionSizing: this.getPositionSizing(stock.riskLevel),
      timing: this.getTradingTiming(stock)
    };
  }

  /**
   * 计算目标价位
   * @param {Object} stock - 股票信息
   * @return {Object} 目标价位信息
   */
  calculateTargetPrice(stock) {
    const currentPrice = stock.currentPrice;
    const expectedReturn = stock.expectedReturn;
    const score = stock.totalScore;

    // 基于预期收益率计算目标价
    const baseTarget = currentPrice * (1 + expectedReturn);

    // 基于评分调整目标价
    let multiplier = 1;
    if (score >= 90) {
      multiplier = 1.15; // 高分股票，提高目标价
    } else if (score >= 80) {
      multiplier = 1.10;
    } else if (score >= 70) {
      multiplier = 1.05;
    }

    const targetPrice = baseTarget * multiplier;

    return {
      target: Math.round(targetPrice * 100) / 100,
      upside: Math.round(((targetPrice - currentPrice) / currentPrice) * 10000) / 100, // 百分比
      confidence: this.getConfidenceLevel(score),
      timeframe: '3-7个交易日'
    };
  }

  /**
   * 获取推荐等级
   * @param {number} score - 综合评分
   * @return {string} 推荐等级
   */
  getRecommendationLevel(score) {
    if (score >= 85) {
      return 'strong_buy';
    } else if (score >= 75) {
      return 'buy';
    } else if (score >= 65) {
      return 'moderate_buy';
    } else {
      return 'hold';
    }
  }

  /**
   * 获取仓位建议
   * @param {string} riskLevel - 风险等级
   * @return {string} 仓位建议
   */
  getPositionSizing(riskLevel) {
    switch (riskLevel) {
      case 'low':
        return '可适当加大仓位，建议5-10%';
      case 'medium':
        return '标准仓位，建议3-5%';
      case 'high':
        return '控制仓位，建议1-3%';
      default:
        return '标准仓位，建议3-5%';
    }
  }

  /**
   * 获取交易时机建议
   * @param {Object} stock - 股票信息
   * @return {string} 时机建议
   */
  getTradingTiming(stock) {
    if (stock.totalScore >= 85) {
      return '可立即关注，适合短期操作';
    } else if (stock.totalScore >= 75) {
      return '建议等待回调机会买入';
    } else {
      return '建议观察，等待更好时机';
    }
  }

  /**
   * 获取置信度等级
   * @param {number} score - 评分
   * @return {string} 置信度
   */
  getConfidenceLevel(score) {
    if (score >= 90) {
      return '高';
    } else if (score >= 80) {
      return '中高';
    } else if (score >= 70) {
      return '中等';
    } else {
      return '较低';
    }
  }

  /**
   * 保存推荐记录
   * @param {Array} recommendations - 推荐列表
   * @param {Object} options - 推荐选项
   */
  async saveRecommendationRecord(recommendations, options) {
    const { ctx } = this;

    try {
      // 这里可以保存到数据库，用于后续的准确率统计
      // 暂时使用日志记录
      ctx.logger.info('智能推荐记录:', {
        count: recommendations.length,
        options,
        timestamp: new Date(),
        recommendations: recommendations.map(r => ({
          symbol: r.symbol,
          score: r.totalScore,
          recommendation: r.recommendation
        }))
      });

    } catch (error) {
      ctx.logger.error('保存推荐记录失败:', error);
    }
  }

  /**
   * 获取推荐历史统计
   * @param {number} days - 统计天数
   * @return {Object} 历史统计信息
   */
  async getRecommendationStats(days = 30) {
    // 这里应该从数据库查询历史推荐记录并计算准确率
    // 暂时返回模拟数据
    return {
      totalRecommendations: 150,
      successfulRecommendations: 98,
      successRate: 65.3,
      averageReturn: 4.2,
      period: `最近${days}天`,
      riskDistribution: {
        low: 45,
        medium: 78,
        high: 27
      }
    };
  }

  /**
   * 简化的技术分析评分
   * @param {Array} data - 历史数据
   * @return {number} 技术分析评分 (0-100)
   */
  calculateSimpleTechnicalScore(data) {
    if (!data || data.length < 5) return 65; // 提高默认评分

    const recent = data.slice(-5); // 最近5天
    const prices = recent.map(d => parseFloat(d.close || d.price || 0));

    // 简单趋势分析：价格是否上涨
    const trend = prices[prices.length - 1] > prices[0] ? 15 : -5; // 减少负面影响

    // 简单波动分析：波动率
    const volatility = this.calculateVolatility(prices);
    const volatilityScore = volatility < 0.05 ? 15 : (volatility > 0.1 ? -5 : 10); // 减少负面影响

    return Math.max(40, Math.min(100, 65 + trend + volatilityScore)); // 提高基础分和最低分
  }

  /**
   * 简化的量价分析评分
   * @param {Array} data - 历史数据
   * @return {number} 量价分析评分 (0-100)
   */
  calculateSimpleVolumeScore(data) {
    if (!data || data.length < 5) return 60; // 提高默认评分

    const recent = data.slice(-5);
    const volumes = recent.map(d => parseFloat(d.volume || d.vol || 0));
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;

    // 成交量是否活跃
    const recentVolume = volumes[volumes.length - 1];
    const volumeScore = recentVolume > avgVolume * 1.2 ? 15 : (recentVolume < avgVolume * 0.8 ? -5 : 10); // 减少负面影响

    return Math.max(45, Math.min(100, 60 + volumeScore)); // 提高基础分和最低分
  }

  /**
   * 简化的趋势分析评分
   * @param {Array} data - 历史数据
   * @return {number} 趋势分析评分 (0-100)
   */
  calculateSimpleTrendScore(data) {
    if (!data || data.length < 5) return 65; // 提高默认评分

    try {
      const prices = data.map(d => parseFloat(d.close || d.price || 0));

      // 简单趋势：比较最近价格和之前价格
      const recentPrice = prices[prices.length - 1];
      const earlierPrice = prices[Math.max(0, prices.length - 5)];

      // 价格趋势评分
      const priceChange = (recentPrice - earlierPrice) / earlierPrice;
      let trendScore = 0;

      if (priceChange > 0.02) {
        trendScore = 15; // 上涨超过2%
      } else if (priceChange > 0) {
        trendScore = 10; // 小幅上涨
      } else if (priceChange > -0.02) {
        trendScore = 5; // 小幅下跌
      } else {
        trendScore = 0; // 大幅下跌
      }

      const result = Math.max(50, Math.min(100, 65 + trendScore)); // 提高基础分和最低分
      return result;
    } catch (error) {
      return 65; // 出错时返回默认评分
    }
  }

  /**
   * 简化的动量分析评分
   * @param {Array} data - 历史数据
   * @return {number} 动量分析评分 (0-100)
   */
  calculateSimpleMomentumScore(data) {
    if (!data || data.length < 3) return 55; // 提高默认评分

    const recent = data.slice(-3);
    const prices = recent.map(d => parseFloat(d.close || d.price || 0));

    // 简单动量：连续上涨天数
    let upDays = 0;
    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > prices[i - 1]) upDays++;
    }

    const momentumScore = upDays >= 2 ? 15 : (upDays === 1 ? 8 : -5); // 减少负面影响

    return Math.max(40, Math.min(100, 55 + momentumScore)); // 提高基础分和最低分
  }

  /**
   * 计算移动平均线
   * @param {Array} prices - 价格数组
   * @param {number} period - 周期
   * @return {number} 移动平均值
   */
  calculateMA(prices, period) {
    if (prices.length < period) return prices[prices.length - 1] || 0;

    const recentPrices = prices.slice(-period);
    return recentPrices.reduce((a, b) => a + b, 0) / recentPrices.length;
  }

  /**
   * 计算波动率
   * @param {Array} prices - 价格数组
   * @return {number} 波动率
   */
  calculateVolatility(prices) {
    if (prices.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }

    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  /**
   * 获取日期字符串
   * @param {number} daysOffset - 天数偏移
   * @return {string} 格式化的日期字符串
   */
  getDateString(daysOffset) {
    const date = new Date();
    date.setDate(date.getDate() + daysOffset);
    return date.toISOString().split('T')[0].replace(/-/g, '');
  }

  /**
   * 获取基础股票列表（用于在数据库无数据时的推荐）
   * 这些是真实存在的知名股票代码，不是模拟数据
   */
  getBasicStockList() {
    const { ctx } = this;
    ctx.logger.error('❌ 无法获取股票数据 - 数据库连接失败且无可用数据源');

    // 不再返回硬编码数据，确保系统完全依赖真实数据库
    // 如果数据库无法连接，应该返回空数组并记录错误
    return [];
  }

  /**
   * 计算基础评分（当历史数据不足时使用）
   * @param {Object} stock - 股票基本信息
   * @param {Object} options - 评分选项
   * @return {Object} 基础评分结果
   */
  calculateBasicScore(stock, options) {
    const { ctx } = this;

    try {
      ctx.logger.info(`📊 为股票 ${stock.symbol} 计算基础评分`);

      // 基于行业和股票特征的基础评分
      let baseScore = 65; // 基础分数

      // 行业评分调整
      const industry = stock.industry || '';
      if (industry.includes('银行')) {
        baseScore += 5; // 银行股相对稳定
      } else if (industry.includes('白酒')) {
        baseScore += 8; // 白酒股表现较好
      } else if (industry.includes('科技') || industry.includes('通信')) {
        baseScore += 6; // 科技股有成长性
      } else if (industry.includes('保险')) {
        baseScore += 4; // 保险股稳定性较好
      }

      // 市场评分调整
      const market = stock.market || '';
      if (market.includes('上海')) {
        baseScore += 2; // 上海主板相对稳定
      }

      // 股票代码评分调整
      const symbol = stock.symbol || stock.tsCode || '';
      if (symbol.startsWith('000001') || symbol.startsWith('600036') || symbol.startsWith('600519')) {
        baseScore += 5; // 知名蓝筹股
      }

      // 风险等级调整
      const { riskLevel } = options;
      let riskAdjustment = 0;
      if (riskLevel === 'low') {
        riskAdjustment = industry.includes('银行') ? 5 : -2; // 低风险偏好银行股
      } else if (riskLevel === 'high') {
        riskAdjustment = industry.includes('科技') ? 5 : 0; // 高风险偏好科技股
      }

      const finalScore = Math.min(85, Math.max(50, baseScore + riskAdjustment));

      ctx.logger.info(`✅ 股票 ${stock.symbol} 基础评分: ${finalScore}`);

      return {
        totalScore: finalScore,
        technicalScore: finalScore - 5,
        volumePriceScore: finalScore - 3,
        trendScore: finalScore - 2,
        momentumScore: finalScore - 8,
        riskLevel: this.determineRiskLevel(industry),
        expectedReturn: this.calculateBasicExpectedReturn(finalScore, options),
        currentPrice: null, // 无法获取真实价格时为null
        dataPoints: 0,
        analysisType: 'basic' // 标记为基础分析
      };

    } catch (error) {
      ctx.logger.error(`计算股票 ${stock.symbol} 基础评分失败:`, error);
      return null;
    }
  }

  /**
   * 根据行业确定风险等级
   */
  determineRiskLevel(industry) {
    if (industry.includes('银行') || industry.includes('保险')) {
      return 'low';
    } else if (industry.includes('白酒') || industry.includes('消费')) {
      return 'medium';
    } else {
      return 'medium';
    }
  }

  /**
   * 计算基础预期收益率
   */
  calculateBasicExpectedReturn(score, options) {
    const baseReturn = (score - 60) / 1000; // 基础收益率
    const userExpected = options.expectedReturn || 0.05;

    // 综合用户期望和评分计算
    return Math.max(0.01, Math.min(0.15, (baseReturn + userExpected) / 2));
  }

  /**
   * 获取市场筛选条件
   * @param {string} market - 市场类型
   * @return {string} SQL条件
   */
  getMarketConditions(market) {
    const { ctx } = this;

    switch (market) {
      case 'main': // 主板
        return "(symbol LIKE '600%' OR symbol LIKE '601%' OR symbol LIKE '603%' OR symbol LIKE '000%')";
      case 'sme': // 中小板
        return "symbol LIKE '002%'";
      case 'gem': // 创业板
        return "symbol LIKE '300%'";
      case 'star': // 科创板
        return "symbol LIKE '688%'";
      case 'beijing': // 北交所
        return "(symbol LIKE '8%' OR symbol LIKE '4%')";
      default:
        ctx.logger.warn(`未知的市场类型: ${market}`);
        return null;
    }
  }

  /**
   * 获取市值筛选条件
   * @param {string} marketCap - 市值类型
   * @return {string} SQL条件
   */
  getMarketCapConditions(marketCap) {
    const { ctx } = this;

    switch (marketCap) {
      case 'large': // 大盘股（主要是600、000开头的知名股票）
        return "(symbol LIKE '600%' OR symbol LIKE '000001%' OR symbol LIKE '000002%')";
      case 'medium': // 中盘股（002开头和部分600开头）
        return "(symbol LIKE '002%' OR (symbol LIKE '600%' AND symbol NOT IN ('600000', '600036', '600519')))";
      case 'small': // 小盘股（300开头和688开头）
        return "(symbol LIKE '300%' OR symbol LIKE '688%')";
      default:
        ctx.logger.warn(`未知的市值类型: ${marketCap}`);
        return null;
    }
  }

  /**
   * 获取排序条件
   * @param {string} riskLevel - 风险等级
   * @param {number} timeHorizon - 投资时间范围
   * @return {string} ORDER BY子句
   */
  getOrderByClause(riskLevel, timeHorizon) {
    // 根据风险等级和投资期限调整排序策略
    if (riskLevel === 'low') {
      // 低风险：优先选择银行、保险等稳定行业
      return "CASE WHEN industry LIKE '%银行%' THEN 1 WHEN industry LIKE '%保险%' THEN 2 ELSE 3 END, symbol ASC";
    } else if (riskLevel === 'high') {
      // 高风险：优先选择科技、新能源等成长性行业
      return "CASE WHEN industry LIKE '%科技%' THEN 1 WHEN industry LIKE '%新能源%' THEN 2 WHEN industry LIKE '%医药%' THEN 3 ELSE 4 END, symbol ASC";
    } else if (timeHorizon <= 7) {
      // 短期投资：随机排序增加多样性
      return "RAND(), symbol ASC";
    } else {
      // 中等风险或长期投资：平衡排序
      return "CASE WHEN industry LIKE '%白酒%' THEN 1 WHEN industry LIKE '%银行%' THEN 2 WHEN industry LIKE '%科技%' THEN 3 ELSE 4 END, symbol ASC";
    }
  }

  /**
   * 根据用户偏好调整股票评分
   * @param {number} baseScore - 基础评分
   * @param {Object} stock - 股票信息
   * @param {Object} options - 用户偏好选项
   * @return {number} 调整后的评分
   */
  adjustScoreByUserPreference(baseScore, stock, options) {
    const { riskLevel, timeHorizon, expectedReturn, industry: userIndustry, market: userMarket, marketCap: userMarketCap } = options;
    let adjustedScore = baseScore;
    let adjustmentDetails = [];

    // 1. 风险等级偏好调整
    const riskAdjustment = this.getRiskAdjustment(stock, riskLevel);
    adjustedScore += riskAdjustment;
    if (riskAdjustment !== 0) {
      adjustmentDetails.push(`风险偏好: ${riskAdjustment > 0 ? '+' : ''}${riskAdjustment}`);
    }

    // 2. 投资期限偏好调整
    const timeAdjustment = this.getTimeHorizonAdjustment(stock, timeHorizon);
    adjustedScore += timeAdjustment;
    if (timeAdjustment !== 0) {
      adjustmentDetails.push(`期限偏好: ${timeAdjustment > 0 ? '+' : ''}${timeAdjustment}`);
    }

    // 3. 预期收益调整
    const returnAdjustment = this.getExpectedReturnAdjustment(stock, expectedReturn);
    adjustedScore += returnAdjustment;
    if (returnAdjustment !== 0) {
      adjustmentDetails.push(`收益偏好: ${returnAdjustment > 0 ? '+' : ''}${returnAdjustment}`);
    }

    // 4. 行业偏好调整
    if (userIndustry && userIndustry !== 'all') {
      const industryAdjustment = this.getIndustryAdjustment(stock, userIndustry);
      adjustedScore += industryAdjustment;
      if (industryAdjustment !== 0) {
        adjustmentDetails.push(`行业偏好: ${industryAdjustment > 0 ? '+' : ''}${industryAdjustment}`);
      }
    }

    // 5. 市场板块偏好调整
    if (userMarket && userMarket !== 'all') {
      const marketAdjustment = this.getMarketAdjustment(stock, userMarket);
      adjustedScore += marketAdjustment;
      if (marketAdjustment !== 0) {
        adjustmentDetails.push(`板块偏好: ${marketAdjustment > 0 ? '+' : ''}${marketAdjustment}`);
      }
    }

    // 6. 市值偏好调整
    if (userMarketCap && userMarketCap !== 'all') {
      const marketCapAdjustment = this.getMarketCapAdjustment(stock, userMarketCap);
      adjustedScore += marketCapAdjustment;
      if (marketCapAdjustment !== 0) {
        adjustmentDetails.push(`市值偏好: ${marketCapAdjustment > 0 ? '+' : ''}${marketCapAdjustment}`);
      }
    }

    // 记录调整详情
    if (adjustmentDetails.length > 0) {
      this.ctx.logger.debug(`股票 ${stock.symbol} 偏好调整: ${adjustmentDetails.join(', ')}`);
    }

    return Math.max(0, Math.min(100, adjustedScore));
  }

  /**
   * 获取偏好调整值（用于日志显示）
   */
  getPreferenceAdjustment(stock, options) {
    return this.adjustScoreByUserPreference(0, stock, options);
  }

  /**
   * 获取风险偏好调整分数
   */
  getRiskAdjustment(stock, riskLevel) {
    const industry = stock.industry || '';
    const symbol = stock.symbol || '';

    switch (riskLevel) {
      case 'low':
        // 低风险偏好：银行、保险、公用事业加分
        if (industry.includes('银行')) return 8;
        if (industry.includes('保险')) return 6;
        if (industry.includes('电力') || industry.includes('水务')) return 5;
        if (industry.includes('白酒')) return 4;
        // 高风险行业减分
        if (industry.includes('科技') || industry.includes('新能源')) return -3;
        if (symbol.startsWith('300') || symbol.startsWith('688')) return -2;
        return 0;

      case 'high':
        // 高风险偏好：科技、新能源、医药加分
        if (industry.includes('科技') || industry.includes('软件')) return 8;
        if (industry.includes('新能源') || industry.includes('电池')) return 7;
        if (industry.includes('医药') || industry.includes('生物')) return 6;
        if (symbol.startsWith('300') || symbol.startsWith('688')) return 5;
        // 传统稳定行业减分
        if (industry.includes('银行') || industry.includes('保险')) return -2;
        return 0;

      case 'medium':
      default:
        // 中等风险：平衡配置，白酒、消费略加分
        if (industry.includes('白酒')) return 3;
        if (industry.includes('消费') || industry.includes('食品')) return 2;
        return 0;
    }
  }

  /**
   * 获取投资期限偏好调整分数
   */
  getTimeHorizonAdjustment(stock, timeHorizon) {
    const industry = stock.industry || '';
    const symbol = stock.symbol || '';

    if (timeHorizon <= 7) {
      // 短期投资：偏好活跃股票
      if (symbol.startsWith('300') || symbol.startsWith('688')) return 4;
      if (industry.includes('科技') || industry.includes('新能源')) return 3;
      if (industry.includes('银行')) return -2; // 银行股短期波动小
      return 0;
    } else if (timeHorizon >= 30) {
      // 长期投资：偏好价值股
      if (industry.includes('白酒')) return 5;
      if (industry.includes('银行') || industry.includes('保险')) return 4;
      if (industry.includes('消费')) return 3;
      return 0;
    } else {
      // 中期投资：平衡
      return 0;
    }
  }

  /**
   * 获取预期收益调整分数
   */
  getExpectedReturnAdjustment(stock, expectedReturn) {
    const industry = stock.industry || '';

    if (expectedReturn >= 0.08) {
      // 高收益期望：偏好成长股
      if (industry.includes('科技') || industry.includes('新能源')) return 5;
      if (industry.includes('医药')) return 4;
      return 0;
    } else if (expectedReturn <= 0.03) {
      // 低收益期望：偏好稳定股
      if (industry.includes('银行') || industry.includes('保险')) return 4;
      if (industry.includes('公用事业')) return 3;
      return 0;
    } else {
      // 中等收益期望
      return 0;
    }
  }

  /**
   * 获取行业偏好调整分数
   */
  getIndustryAdjustment(stock, userIndustry) {
    const industry = stock.industry || '';

    // 如果股票行业匹配用户选择的行业，给予高分
    if (industry.includes(userIndustry)) {
      return 10; // 行业匹配给予较高加分
    }
    return 0;
  }

  /**
   * 获取市场板块偏好调整分数
   */
  getMarketAdjustment(stock, userMarket) {
    const symbol = stock.symbol || '';

    switch (userMarket) {
      case 'main': // 主板
        if (symbol.startsWith('600') || symbol.startsWith('601') || symbol.startsWith('603') || symbol.startsWith('000')) {
          return 8;
        }
        break;
      case 'sme': // 中小板
        if (symbol.startsWith('002')) {
          return 8;
        }
        break;
      case 'gem': // 创业板
        if (symbol.startsWith('300')) {
          return 8;
        }
        break;
      case 'star': // 科创板
        if (symbol.startsWith('688')) {
          return 8;
        }
        break;
    }
    return 0;
  }

  /**
   * 获取市值偏好调整分数
   */
  getMarketCapAdjustment(stock, userMarketCap) {
    const symbol = stock.symbol || '';
    const industry = stock.industry || '';

    switch (userMarketCap) {
      case 'large': // 大盘股偏好
        if (symbol.startsWith('600') || symbol.includes('000001') || symbol.includes('000002')) {
          return 6;
        }
        if (industry.includes('银行') || industry.includes('白酒')) {
          return 4;
        }
        break;
      case 'medium': // 中盘股偏好
        if (symbol.startsWith('002')) {
          return 6;
        }
        break;
      case 'small': // 小盘股偏好
        if (symbol.startsWith('300') || symbol.startsWith('688')) {
          return 6;
        }
        break;
    }
    return 0;
  }
}

module.exports = SmartRecommendationService;
