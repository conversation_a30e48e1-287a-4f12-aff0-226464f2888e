[{"name": "Process Start", "start": 1753440749842, "end": 1753440751504, "duration": 1662, "pid": 33220, "index": 0}, {"name": "Application Start", "start": 1753440751506, "end": 1753440754299, "duration": 2793, "pid": 33220, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753440751527, "end": 1753440751571, "duration": 44, "pid": 33220, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753440751571, "end": 1753440751631, "duration": 60, "pid": 33220, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753440751572, "end": 1753440751573, "duration": 1, "pid": 33220, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753440751579, "end": 1753440751580, "duration": 1, "pid": 33220, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753440751582, "end": 1753440751583, "duration": 1, "pid": 33220, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753440751584, "end": 1753440751585, "duration": 1, "pid": 33220, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753440751588, "end": 1753440751589, "duration": 1, "pid": 33220, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753440751590, "end": 1753440751591, "duration": 1, "pid": 33220, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753440751592, "end": 1753440751593, "duration": 1, "pid": 33220, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753440751594, "end": 1753440751595, "duration": 1, "pid": 33220, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753440751598, "end": 1753440751599, "duration": 1, "pid": 33220, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753440751601, "end": 1753440751602, "duration": 1, "pid": 33220, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753440751603, "end": 1753440751603, "duration": 0, "pid": 33220, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753440751604, "end": 1753440751605, "duration": 1, "pid": 33220, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753440751606, "end": 1753440751606, "duration": 0, "pid": 33220, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753440751608, "end": 1753440751609, "duration": 1, "pid": 33220, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753440751610, "end": 1753440751610, "duration": 0, "pid": 33220, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753440751611, "end": 1753440751611, "duration": 0, "pid": 33220, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753440751612, "end": 1753440751612, "duration": 0, "pid": 33220, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753440751613, "end": 1753440751614, "duration": 1, "pid": 33220, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753440751615, "end": 1753440751616, "duration": 1, "pid": 33220, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753440751617, "end": 1753440751618, "duration": 1, "pid": 33220, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753440751620, "end": 1753440751620, "duration": 0, "pid": 33220, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753440751621, "end": 1753440751622, "duration": 1, "pid": 33220, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753440751624, "end": 1753440751624, "duration": 0, "pid": 33220, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753440751627, "end": 1753440751627, "duration": 0, "pid": 33220, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753440751629, "end": 1753440751630, "duration": 1, "pid": 33220, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753440751630, "end": 1753440751630, "duration": 0, "pid": 33220, "index": 29}, {"name": "Load extend/application.js", "start": 1753440751633, "end": 1753440751745, "duration": 112, "pid": 33220, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753440751634, "end": 1753440751635, "duration": 1, "pid": 33220, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753440751636, "end": 1753440751638, "duration": 2, "pid": 33220, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753440751639, "end": 1753440751646, "duration": 7, "pid": 33220, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753440751649, "end": 1753440751657, "duration": 8, "pid": 33220, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753440751659, "end": 1753440751661, "duration": 2, "pid": 33220, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753440751662, "end": 1753440751666, "duration": 4, "pid": 33220, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753440751667, "end": 1753440751733, "duration": 66, "pid": 33220, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753440751736, "end": 1753440751739, "duration": 3, "pid": 33220, "index": 38}, {"name": "Load extend/request.js", "start": 1753440751745, "end": 1753440751763, "duration": 18, "pid": 33220, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753440751753, "end": 1753440751755, "duration": 2, "pid": 33220, "index": 40}, {"name": "Load extend/response.js", "start": 1753440751763, "end": 1753440751781, "duration": 18, "pid": 33220, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753440751770, "end": 1753440751773, "duration": 3, "pid": 33220, "index": 42}, {"name": "Load extend/context.js", "start": 1753440751781, "end": 1753440751875, "duration": 94, "pid": 33220, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753440751783, "end": 1753440751808, "duration": 25, "pid": 33220, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753440751809, "end": 1753440751812, "duration": 3, "pid": 33220, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753440751814, "end": 1753440751815, "duration": 1, "pid": 33220, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753440751817, "end": 1753440751853, "duration": 36, "pid": 33220, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753440751855, "end": 1753440751857, "duration": 2, "pid": 33220, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753440751859, "end": 1753440751860, "duration": 1, "pid": 33220, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753440751861, "end": 1753440751865, "duration": 4, "pid": 33220, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753440751866, "end": 1753440751867, "duration": 1, "pid": 33220, "index": 51}, {"name": "Load extend/helper.js", "start": 1753440751875, "end": 1753440751932, "duration": 57, "pid": 33220, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753440751876, "end": 1753440751912, "duration": 36, "pid": 33220, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753440751919, "end": 1753440751920, "duration": 1, "pid": 33220, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753440751921, "end": 1753440751921, "duration": 0, "pid": 33220, "index": 55}, {"name": "Load app.js", "start": 1753440751932, "end": 1753440752079, "duration": 147, "pid": 33220, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753440751933, "end": 1753440751934, "duration": 1, "pid": 33220, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753440751934, "end": 1753440751941, "duration": 7, "pid": 33220, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753440751943, "end": 1753440751965, "duration": 22, "pid": 33220, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753440751966, "end": 1753440751990, "duration": 24, "pid": 33220, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753440751991, "end": 1753440752020, "duration": 29, "pid": 33220, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753440752021, "end": 1753440752023, "duration": 2, "pid": 33220, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753440752024, "end": 1753440752028, "duration": 4, "pid": 33220, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753440752029, "end": 1753440752033, "duration": 4, "pid": 33220, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753440752033, "end": 1753440752039, "duration": 6, "pid": 33220, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753440752040, "end": 1753440752040, "duration": 0, "pid": 33220, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753440752042, "end": 1753440752043, "duration": 1, "pid": 33220, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753440752043, "end": 1753440752044, "duration": 1, "pid": 33220, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753440752045, "end": 1753440752045, "duration": 0, "pid": 33220, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753440752046, "end": 1753440752051, "duration": 5, "pid": 33220, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753440752052, "end": 1753440752076, "duration": 24, "pid": 33220, "index": 71}, {"name": "Require(62) app.js", "start": 1753440752078, "end": 1753440752078, "duration": 0, "pid": 33220, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753440752100, "end": 1753440754259, "duration": 2159, "pid": 33220, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753440752958, "end": 1753440753111, "duration": 153, "pid": 33220, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753440753011, "end": 1753440754124, "duration": 1113, "pid": 33220, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753440753218, "end": 1753440754294, "duration": 1076, "pid": 33220, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753440753219, "end": 1753440753839, "duration": 620, "pid": 33220, "index": 77}, {"name": "Load Service", "start": 1753440753219, "end": 1753440753431, "duration": 212, "pid": 33220, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753440753219, "end": 1753440753431, "duration": 212, "pid": 33220, "index": 79}, {"name": "Load Middleware", "start": 1753440753432, "end": 1753440753661, "duration": 229, "pid": 33220, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753440753432, "end": 1753440753644, "duration": 212, "pid": 33220, "index": 81}, {"name": "Load Controller", "start": 1753440753661, "end": 1753440753776, "duration": 115, "pid": 33220, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753440753661, "end": 1753440753776, "duration": 115, "pid": 33220, "index": 83}, {"name": "Load Router", "start": 1753440753776, "end": 1753440753805, "duration": 29, "pid": 33220, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753440753777, "end": 1753440753778, "duration": 1, "pid": 33220, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753440753782, "end": 1753440753838, "duration": 56, "pid": 33220, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753440754295, "end": 1753440754295, "duration": 0, "pid": 33220, "index": 87}]