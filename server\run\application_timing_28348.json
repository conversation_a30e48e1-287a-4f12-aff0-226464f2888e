[{"name": "Process Start", "start": 1753440753721, "end": 1753440755751, "duration": 2030, "pid": 28348, "index": 0}, {"name": "Application Start", "start": 1753440755753, "end": 1753440758055, "duration": 2302, "pid": 28348, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753440755771, "end": 1753440755810, "duration": 39, "pid": 28348, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753440755810, "end": 1753440755864, "duration": 54, "pid": 28348, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753440755811, "end": 1753440755812, "duration": 1, "pid": 28348, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753440755817, "end": 1753440755818, "duration": 1, "pid": 28348, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753440755819, "end": 1753440755819, "duration": 0, "pid": 28348, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753440755820, "end": 1753440755821, "duration": 1, "pid": 28348, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753440755822, "end": 1753440755823, "duration": 1, "pid": 28348, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753440755824, "end": 1753440755825, "duration": 1, "pid": 28348, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753440755825, "end": 1753440755826, "duration": 1, "pid": 28348, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753440755827, "end": 1753440755827, "duration": 0, "pid": 28348, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753440755828, "end": 1753440755829, "duration": 1, "pid": 28348, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753440755830, "end": 1753440755831, "duration": 1, "pid": 28348, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753440755832, "end": 1753440755833, "duration": 1, "pid": 28348, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753440755834, "end": 1753440755834, "duration": 0, "pid": 28348, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753440755835, "end": 1753440755836, "duration": 1, "pid": 28348, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753440755836, "end": 1753440755837, "duration": 1, "pid": 28348, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753440755838, "end": 1753440755839, "duration": 1, "pid": 28348, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753440755840, "end": 1753440755840, "duration": 0, "pid": 28348, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753440755841, "end": 1753440755841, "duration": 0, "pid": 28348, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753440755842, "end": 1753440755843, "duration": 1, "pid": 28348, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753440755844, "end": 1753440755844, "duration": 0, "pid": 28348, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753440755845, "end": 1753440755846, "duration": 1, "pid": 28348, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753440755849, "end": 1753440755849, "duration": 0, "pid": 28348, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753440755851, "end": 1753440755852, "duration": 1, "pid": 28348, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753440755855, "end": 1753440755855, "duration": 0, "pid": 28348, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753440755859, "end": 1753440755859, "duration": 0, "pid": 28348, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753440755862, "end": 1753440755863, "duration": 1, "pid": 28348, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753440755863, "end": 1753440755863, "duration": 0, "pid": 28348, "index": 29}, {"name": "Load extend/application.js", "start": 1753440755864, "end": 1753440755973, "duration": 109, "pid": 28348, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753440755866, "end": 1753440755866, "duration": 0, "pid": 28348, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753440755867, "end": 1753440755869, "duration": 2, "pid": 28348, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753440755870, "end": 1753440755876, "duration": 6, "pid": 28348, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753440755878, "end": 1753440755886, "duration": 8, "pid": 28348, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753440755888, "end": 1753440755890, "duration": 2, "pid": 28348, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753440755891, "end": 1753440755893, "duration": 2, "pid": 28348, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753440755894, "end": 1753440755957, "duration": 63, "pid": 28348, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753440755959, "end": 1753440755964, "duration": 5, "pid": 28348, "index": 38}, {"name": "Load extend/request.js", "start": 1753440755973, "end": 1753440755994, "duration": 21, "pid": 28348, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753440755983, "end": 1753440755985, "duration": 2, "pid": 28348, "index": 40}, {"name": "Load extend/response.js", "start": 1753440755994, "end": 1753440756017, "duration": 23, "pid": 28348, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753440756003, "end": 1753440756006, "duration": 3, "pid": 28348, "index": 42}, {"name": "Load extend/context.js", "start": 1753440756017, "end": 1753440756094, "duration": 77, "pid": 28348, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753440756018, "end": 1753440756036, "duration": 18, "pid": 28348, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753440756036, "end": 1753440756039, "duration": 3, "pid": 28348, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753440756040, "end": 1753440756041, "duration": 1, "pid": 28348, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753440756043, "end": 1753440756070, "duration": 27, "pid": 28348, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753440756073, "end": 1753440756074, "duration": 1, "pid": 28348, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753440756077, "end": 1753440756077, "duration": 0, "pid": 28348, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753440756079, "end": 1753440756084, "duration": 5, "pid": 28348, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753440756084, "end": 1753440756085, "duration": 1, "pid": 28348, "index": 51}, {"name": "Load extend/helper.js", "start": 1753440756094, "end": 1753440756149, "duration": 55, "pid": 28348, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753440756096, "end": 1753440756129, "duration": 33, "pid": 28348, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753440756137, "end": 1753440756138, "duration": 1, "pid": 28348, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753440756139, "end": 1753440756139, "duration": 0, "pid": 28348, "index": 55}, {"name": "Load app.js", "start": 1753440756149, "end": 1753440756236, "duration": 87, "pid": 28348, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753440756150, "end": 1753440756150, "duration": 0, "pid": 28348, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753440756151, "end": 1753440756155, "duration": 4, "pid": 28348, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753440756157, "end": 1753440756170, "duration": 13, "pid": 28348, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753440756171, "end": 1753440756186, "duration": 15, "pid": 28348, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753440756186, "end": 1753440756200, "duration": 14, "pid": 28348, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753440756201, "end": 1753440756202, "duration": 1, "pid": 28348, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753440756202, "end": 1753440756204, "duration": 2, "pid": 28348, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753440756205, "end": 1753440756205, "duration": 0, "pid": 28348, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753440756206, "end": 1753440756206, "duration": 0, "pid": 28348, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753440756207, "end": 1753440756207, "duration": 0, "pid": 28348, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753440756209, "end": 1753440756209, "duration": 0, "pid": 28348, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753440756210, "end": 1753440756210, "duration": 0, "pid": 28348, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753440756214, "end": 1753440756214, "duration": 0, "pid": 28348, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753440756215, "end": 1753440756217, "duration": 2, "pid": 28348, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753440756218, "end": 1753440756233, "duration": 15, "pid": 28348, "index": 71}, {"name": "Require(62) app.js", "start": 1753440756235, "end": 1753440756235, "duration": 0, "pid": 28348, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753440756247, "end": 1753440758042, "duration": 1795, "pid": 28348, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753440756957, "end": 1753440757105, "duration": 148, "pid": 28348, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753440757001, "end": 1753440758006, "duration": 1005, "pid": 28348, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753440757170, "end": 1753440758054, "duration": 884, "pid": 28348, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753440757171, "end": 1753440757728, "duration": 557, "pid": 28348, "index": 77}, {"name": "Load Service", "start": 1753440757171, "end": 1753440757360, "duration": 189, "pid": 28348, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753440757172, "end": 1753440757360, "duration": 188, "pid": 28348, "index": 79}, {"name": "Load Middleware", "start": 1753440757361, "end": 1753440757551, "duration": 190, "pid": 28348, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753440757361, "end": 1753440757530, "duration": 169, "pid": 28348, "index": 81}, {"name": "Load Controller", "start": 1753440757551, "end": 1753440757684, "duration": 133, "pid": 28348, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753440757551, "end": 1753440757684, "duration": 133, "pid": 28348, "index": 83}, {"name": "Load Router", "start": 1753440757684, "end": 1753440757706, "duration": 22, "pid": 28348, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753440757684, "end": 1753440757686, "duration": 2, "pid": 28348, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753440757687, "end": 1753440757726, "duration": 39, "pid": 28348, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753440758054, "end": 1753440758054, "duration": 0, "pid": 28348, "index": 87}]