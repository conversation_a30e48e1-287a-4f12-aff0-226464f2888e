[{"name": "Process Start", "start": 1753440743676, "end": 1753440747807, "duration": 4131, "pid": 23988, "index": 0}, {"name": "Application Start", "start": 1753440747809, "end": 1753440749651, "duration": 1842, "pid": 23988, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753440747833, "end": 1753440747878, "duration": 45, "pid": 23988, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753440747878, "end": 1753440747938, "duration": 60, "pid": 23988, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753440747879, "end": 1753440747880, "duration": 1, "pid": 23988, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753440747888, "end": 1753440747888, "duration": 0, "pid": 23988, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753440747890, "end": 1753440747891, "duration": 1, "pid": 23988, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753440747892, "end": 1753440747892, "duration": 0, "pid": 23988, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753440747894, "end": 1753440747894, "duration": 0, "pid": 23988, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753440747895, "end": 1753440747896, "duration": 1, "pid": 23988, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753440747897, "end": 1753440747898, "duration": 1, "pid": 23988, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753440747900, "end": 1753440747901, "duration": 1, "pid": 23988, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753440747902, "end": 1753440747903, "duration": 1, "pid": 23988, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753440747904, "end": 1753440747904, "duration": 0, "pid": 23988, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753440747905, "end": 1753440747905, "duration": 0, "pid": 23988, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753440747906, "end": 1753440747907, "duration": 1, "pid": 23988, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753440747907, "end": 1753440747908, "duration": 1, "pid": 23988, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753440747909, "end": 1753440747909, "duration": 0, "pid": 23988, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753440747910, "end": 1753440747910, "duration": 0, "pid": 23988, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753440747911, "end": 1753440747911, "duration": 0, "pid": 23988, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753440747912, "end": 1753440747913, "duration": 1, "pid": 23988, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753440747913, "end": 1753440747914, "duration": 1, "pid": 23988, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753440747915, "end": 1753440747916, "duration": 1, "pid": 23988, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753440747917, "end": 1753440747918, "duration": 1, "pid": 23988, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753440747920, "end": 1753440747920, "duration": 0, "pid": 23988, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753440747923, "end": 1753440747923, "duration": 0, "pid": 23988, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753440747926, "end": 1753440747926, "duration": 0, "pid": 23988, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753440747929, "end": 1753440747930, "duration": 1, "pid": 23988, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753440747937, "end": 1753440747938, "duration": 1, "pid": 23988, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753440747938, "end": 1753440747938, "duration": 0, "pid": 23988, "index": 29}, {"name": "Load extend/agent.js", "start": 1753440747939, "end": 1753440748063, "duration": 124, "pid": 23988, "index": 30}, {"name": "Require(26) node_modules/egg-security/app/extend/agent.js", "start": 1753440747940, "end": 1753440747943, "duration": 3, "pid": 23988, "index": 31}, {"name": "Require(27) node_modules/egg-schedule/app/extend/agent.js", "start": 1753440747945, "end": 1753440748042, "duration": 97, "pid": 23988, "index": 32}, {"name": "Require(28) node_modules/egg-logrotator/app/extend/agent.js", "start": 1753440748044, "end": 1753440748048, "duration": 4, "pid": 23988, "index": 33}, {"name": "Load extend/context.js", "start": 1753440748063, "end": 1753440748153, "duration": 90, "pid": 23988, "index": 34}, {"name": "Require(29) node_modules/egg-security/app/extend/context.js", "start": 1753440748064, "end": 1753440748086, "duration": 22, "pid": 23988, "index": 35}, {"name": "Require(30) node_modules/egg-jsonp/app/extend/context.js", "start": 1753440748087, "end": 1753440748092, "duration": 5, "pid": 23988, "index": 36}, {"name": "Require(31) node_modules/egg-i18n/app/extend/context.js", "start": 1753440748093, "end": 1753440748094, "duration": 1, "pid": 23988, "index": 37}, {"name": "Require(32) node_modules/egg-multipart/app/extend/context.js", "start": 1753440748095, "end": 1753440748129, "duration": 34, "pid": 23988, "index": 38}, {"name": "Require(33) node_modules/egg-view/app/extend/context.js", "start": 1753440748131, "end": 1753440748134, "duration": 3, "pid": 23988, "index": 39}, {"name": "Require(34) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753440748138, "end": 1753440748138, "duration": 0, "pid": 23988, "index": 40}, {"name": "Require(35) node_modules/egg/app/extend/context.js", "start": 1753440748139, "end": 1753440748143, "duration": 4, "pid": 23988, "index": 41}, {"name": "Require(36) app/extend/context.js", "start": 1753440748143, "end": 1753440748144, "duration": 1, "pid": 23988, "index": 42}, {"name": "Load agent.js", "start": 1753440748153, "end": 1753440748245, "duration": 92, "pid": 23988, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1753440748154, "end": 1753440748154, "duration": 0, "pid": 23988, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1753440748155, "end": 1753440748156, "duration": 1, "pid": 23988, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1753440748157, "end": 1753440748179, "duration": 22, "pid": 23988, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1753440748181, "end": 1753440748185, "duration": 4, "pid": 23988, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1753440748186, "end": 1753440748207, "duration": 21, "pid": 23988, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1753440748207, "end": 1753440748208, "duration": 1, "pid": 23988, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1753440748209, "end": 1753440748209, "duration": 0, "pid": 23988, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1753440748211, "end": 1753440748243, "duration": 32, "pid": 23988, "index": 51}, {"name": "Require(45) node_modules/egg/agent.js", "start": 1753440748244, "end": 1753440748245, "duration": 1, "pid": 23988, "index": 52}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753440748256, "end": 1753440749300, "duration": 1044, "pid": 23988, "index": 53}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1753440748257, "end": 1753440749272, "duration": 1015, "pid": 23988, "index": 54}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1753440748257, "end": 1753440749650, "duration": 1393, "pid": 23988, "index": 55}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753440748987, "end": 1753440749129, "duration": 142, "pid": 23988, "index": 56}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753440749034, "end": 1753440749294, "duration": 260, "pid": 23988, "index": 57}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753440749210, "end": 1753440749640, "duration": 430, "pid": 23988, "index": 58}]